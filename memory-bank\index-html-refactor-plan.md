# Refactoring Plan for index.html

**1. Goals:**

*   Improve maintainability by separating concerns (HTML, CSS, JS).
*   Enhance performance by enabling better caching and potentially lazy loading of components.
*   Increase code readability and organization.
*   Facilitate easier updates and feature additions.
*   Promote reusability of UI components and logic.

**2. Proposed File Structure:**

*   `index.html`: Will become the main entry point, primarily containing the basic page skeleton and links to CSS and JS files. Most of the current content will be moved out.
*   `css/`:
    *   `main.css`: Global styles, layout styles (e.g., from `core-styles.css`).
    *   `components/`: Directory for individual component styles.
        *   `_company-config-panel.css`
        *   `_gemini-config-panel.css`
        *   `_input-panel.css`
        *   `_edit-panel.css`
        *   `_preview-panel.css`
        *   `_document-specific.css` (for styles that vary by document type like receipt, invoice)
    *   `themes/`: (If multiple themes are planned)
    *   `print.css`: (Already exists, keep and refine)
    *   `a4-template-optimization.css`: (Already exists, keep and refine)
    *   `driver-agreement.css`: (Already exists, potentially merge or make component-specific)
    *   `stamp-position.css`: (Already exists, potentially merge or make component-specific)
    *   `template-image.css`: (Already exists, potentially merge or make component-specific)
*   `js/`:
    *   `main.js` (or `app.js`): Main application logic, initialization, event listeners for core interactions.
    *   `config.js`: Application configuration, API keys (though hardcoding Gemini API key is a rule, other configs can go here).
    *   `ui/`:
        *   `dom-utils.js`: Helper functions for DOM manipulation.
        *   `event-handlers.js`: Centralized event handlers if not component-specific.
        *   `components/`: JavaScript logic for individual UI components.
            *   `companyConfigPanel.js`
            *   `geminiConfigPanel.js`
            *   `nlpInput.js` (handling natural language input and image upload)
            *   `documentEditor.js` (handling form fields, item additions/deletions)
            *   `documentPreview.js` (handling preview updates, zoom, export)
            *   `collapsiblePanels.js` (logic for collapsible panels)
        *   `stateManager.js`: (Optional, for more complex state management if needed beyond simple DOM updates).
    *   `services/`:
        *   `apiService.js`: (If Gemini or other APIs are called from JS beyond the initial setup).
        *   `nlpService.js`: Logic for local NLP processing or interfacing with Gemini.
        *   `pdfExportService.js`: (Refined logic from `index.html` for PDF export).
        *   `printService.js`: (Refined logic for printing).
        *   `resourceLoader.js`: (Refined logic for loading local fallbacks of libraries).
    *   `utils/`: General utility functions (date formatting, calculations, etc.).
*   `templates/` (or `partials/`): For HTML snippets if a client-side templating engine is introduced or if HTML is dynamically loaded.
    *   `company-config-panel.html`
    *   `edit-form-invoice.html`
    *   `edit-form-receipt.html`
    *   etc.

**3. Refactoring Steps:**

*   **Phase 1: Preparation & CSS Separation**
    1.  **Backup `index.html`**: Create a copy before starting.
    2.  **Create new CSS files**: Based on the proposed structure (`main.css`, `components/*.css`).
    3.  **Move Inline Styles & `<style>` blocks**: Extract all inline styles and styles within `<style>` tags in `index.html` to the appropriate new CSS files.
    4.  **Link new CSS files**: Update `index.html` to link these new CSS files and remove the old `<style>` blocks and inline styles.
    5.  **Organize existing CSS**: Review `core-styles.css`, `a4-template-optimization.css`, etc. and move relevant rules to `main.css` or component-specific files. Aim to reduce redundancy.
    6.  **Test**: Ensure the page still renders correctly with externalized CSS.

*   **Phase 2: JavaScript Modularization**
    1.  **Create new JS files**: Based on the proposed structure (`main.js`, `ui/components/*.js`, `services/*.js`, `utils.js`).
    2.  **Identify Core Logic**: In `index.html`, identify distinct JavaScript functionalities:
        *   Initialization (event listeners, default values).
        *   Company and document type selection logic.
        *   Currency and payment method logic.
        *   NLP mode selection.
        *   Natural language input parsing (and image upload).
        *   Document field updates (receipt number, date, customer name, etc.).
        *   Service item management (add/remove rows, calculate totals).
        *   Buyer info panel logic.
        *   Document-specific field logic (invoice, quotation, driver agreement).
        *   Preview update, zoom, export (image/PDF), print logic.
        *   Collapsible panel logic.
        *   API status indicator logic.
        *   Dynamic padding adjustment.
        *   Fallback resource loader (`initializeInlineResourceManager`).
    3.  **Extract Functions to Modules**: Move these functionalities into their respective new JS files. Use ES6 modules (`import`/`export`).
        *   Example: `updateTotalAmount` could go into `js/ui/components/documentEditor.js` or a shared `js/utils/calculations.js`.
        *   PDF export logic into `js/services/pdfExportService.js`.
    4.  **Create `main.js`**: This file will import necessary modules and initialize the application (e.g., attach event listeners defined in other modules).
    5.  **Update `index.html`**: Remove all `<script>` blocks containing inline JavaScript. Add a single `<script type="module" src="js/main.js"></script>` at the end of the `<body>`.
    6.  **Manage Dependencies**: Ensure libraries like `html2canvas` and `jsPDF` are properly imported in the modules that use them (e.g., `pdfExportService.js`). Consider using a package manager (npm/yarn) if not already, to manage these external libraries, though the current setup seems to rely on local/CDN fallbacks.
    7.  **Test**: Thoroughly test all functionalities.

*   **Phase 3: HTML Structure & Templating (Optional but Recommended)**
    1.  **Identify Reusable HTML Blocks**: Sections like configuration panels, item rows in the editor, etc.
    2.  **Extract to HTML Partials/Templates**: If using a client-side templating approach or dynamic loading, move these blocks to separate `.html` files in `templates/`.
    3.  **Simplify `index.html`**: `index.html` should mainly contain the overall layout structure (header, main content area placeholders, footer).
    4.  **Dynamic Content Loading**: Implement logic in JavaScript (e.g., in `js/ui/components/*.js` or `main.js`) to fetch and inject these HTML partials into the main page, or use a templating engine.
    5.  **Test**: Ensure all parts of the UI are correctly assembled and functional.

*   **Phase 4: Optimization & Refinement**
    1.  **Code Review**: Review all refactored code for clarity, efficiency, and adherence to standards.
    2.  **Performance**:
        *   Consider lazy loading for JS modules or components not immediately needed.
        *   Optimize images.
        *   Minify CSS and JS for production (if not already part of a build process).
    3.  **Error Handling**: Ensure robust error handling is in place across all modules.
    4.  **Documentation**: Update/create comments in the code. Update Memory Bank documents.

**4. Tools & Technologies:**

*   Keep existing libraries (Tailwind CSS, Font Awesome, html2canvas, jsPDF).
*   Utilize ES6 Modules for JavaScript.
*   (Optional) Consider a simple build tool (like Parcel or Rollup if complexity grows) for bundling and minification, though the current "fallback" system suggests a desire for minimal build steps.

**5. Memory Bank Updates:**

*   This plan will be stored in `memory-bank/index-html-refactor-plan.md`.
*   `active-context.md` should be updated to reflect this refactoring task as ongoing.
*   `architecture-design.md` (if it exists or is created) should be updated with the new proposed frontend structure.