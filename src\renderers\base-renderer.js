/**
 * @file 基础渲染器 - 实现渲染器系统的核心基础类和注册表
 * <AUTHOR> Team
 * @description 
 * 这个文件定义了渲染器系统的基础类和管理组件，包括：
 * - BaseRenderer 基础渲染器类，所有渲染器的父类
 * - RendererRegistry 渲染器注册表，管理所有已注册的渲染器
 * - RenderingEngine 渲染引擎，统一的渲染入口和流程控制
 * - 渲染器配置、验证、缓存等核心功能
 */

// #region 导入依赖模块
import { EventEmitter } from '../core/events/event-emitter.js';
import { EventTypes } from '../core/events/event-types.js';
import { Validator } from '../core/utils/validation.js';
import { DateUtils } from '../core/utils/date-utils.js';
// #endregion

// #region 基础渲染器类定义
/**
 * @class BaseRenderer - 基础渲染器类
 * @description 所有渲染器的基础类，定义了渲染器的基本接口和通用功能
 */
export class BaseRenderer extends EventEmitter {
    /**
     * 构造函数 - 初始化渲染器实例
     * @param {Object} config - 渲染器配置对象
     */
    constructor(config = {}) {
        super();
        
        // 基础配置
        this.name = config.name || 'base-renderer';
        this.type = config.type || 'base';
        this.version = config.version || '1.0.0';
        this.description = config.description || '基础渲染器';
        
        // 元数据信息
        this.metadata = {
            created: new Date(),
            lastModified: new Date(),
            author: 'SmartOffice Team',
            ...config.metadata
        };
        
        // 渲染器配置
        this.renderConfig = {
            // 输出格式配置
            outputFormat: 'html', // html, pdf, image, print等
            outputQuality: 'high', // low, medium, high
            outputCompression: false, // 是否压缩输出
            
            // 性能配置
            enableCache: true, // 是否启用缓存
            maxCacheSize: 100, // 最大缓存项数
            cacheTimeout: 300000, // 缓存超时时间（毫秒）
            enableParallel: false, // 是否启用并行渲染
            
            // 调试配置
            enableDebug: false, // 是否启用调试模式
            enableProfiling: false, // 是否启用性能分析
            logLevel: 'info', // 日志级别
            
            ...config.renderConfig
        };
        
        // 渲染器状态
        this.state = {
            isInitialized: false,
            isRendering: false,
            lastRenderTime: null,
            renderCount: 0,
            errorCount: 0
        };
        
        // 缓存管理
        this.cache = new Map();
        this.cacheStats = {
            hits: 0,
            misses: 0,
            total: 0
        };
        
        // 性能统计
        this.performanceStats = {
            totalRenderTime: 0,
            averageRenderTime: 0,
            minRenderTime: Infinity,
            maxRenderTime: 0,
            renderHistory: []
        };
        
        // 初始化渲染器
        this._initialize();
    }

    /**
     * 初始化渲染器 - 设置渲染器的初始状态和配置
     * @private
     */
    _initialize() {
        try {
            // 验证配置
            this._validateConfig();
            
            // 设置缓存清理定时器
            if (this.renderConfig.enableCache) {
                this._setupCacheCleanup();
            }
            
            // 标记为已初始化
            this.state.isInitialized = true;
            
            // 触发初始化完成事件
            this.emit(EventTypes.RENDERER.INITIALIZED, {
                renderer: this.name,
                type: this.type,
                timestamp: new Date()
            });
            
        } catch (error) {
            this.emit(EventTypes.RENDERER.ERROR, {
                renderer: this.name,
                error: error.message,
                timestamp: new Date()
            });
            throw new Error(`渲染器初始化失败: ${error.message}`);
        }
    }

    /**
     * 验证渲染器配置 - 检查配置的有效性
     * @private
     */
    _validateConfig() {
        const requiredFields = ['name', 'type'];
        
        for (const field of requiredFields) {
            if (!this[field]) {
                throw new Error(`渲染器配置缺少必填字段: ${field}`);
            }
        }
        
        // 验证输出格式
        const validOutputFormats = ['html', 'pdf', 'image', 'print'];
        if (!validOutputFormats.includes(this.renderConfig.outputFormat)) {
            throw new Error(`不支持的输出格式: ${this.renderConfig.outputFormat}`);
        }
    }

    /**
     * 设置缓存清理 - 定期清理过期的缓存项
     * @private
     */
    _setupCacheCleanup() {
        setInterval(() => {
            this._cleanupCache();
        }, this.renderConfig.cacheTimeout);
    }

    /**
     * 清理过期缓存 - 移除过期的缓存项
     * @private
     */
    _cleanupCache() {
        const now = Date.now();
        let cleanedCount = 0;
        
        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > this.renderConfig.cacheTimeout) {
                this.cache.delete(key);
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0 && this.renderConfig.enableDebug) {
            console.log(`渲染器 ${this.name} 清理了 ${cleanedCount} 个过期缓存项`);
        }
    }

    /**
     * 渲染 - 主要的渲染方法，统一的渲染入口
     * @param {Object} template - 模板对象
     * @param {Object} data - 渲染数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 渲染结果
     */
    async render(template, data, options = {}) {
        // 检查渲染器是否已初始化
        if (!this.state.isInitialized) {
            throw new Error(`渲染器 ${this.name} 尚未初始化`);
        }
        
        // 检查是否正在渲染
        if (this.state.isRendering && !options.allowConcurrent) {
            throw new Error(`渲染器 ${this.name} 正在进行渲染，请等待完成`);
        }
        
        const startTime = Date.now();
        const renderOptions = { ...this.renderConfig, ...options };
        
        try {
            // 设置渲染状态
            this.state.isRendering = true;
            
            // 触发渲染开始事件
            this.emit(EventTypes.RENDERER.RENDER_START, {
                renderer: this.name,
                template: template.name,
                timestamp: new Date()
            });
            
            // 预处理数据
            const processedData = await this._preprocessData(data, renderOptions);
            
            // 检查缓存
            const cacheKey = this._generateCacheKey(template, processedData, renderOptions);
            if (this.renderConfig.enableCache && this.cache.has(cacheKey)) {
                const cachedResult = this.cache.get(cacheKey);
                this.cacheStats.hits++;
                this.cacheStats.total++;
                
                this.emit(EventTypes.RENDERER.CACHE_HIT, {
                    renderer: this.name,
                    cacheKey,
                    timestamp: new Date()
                });
                
                return cachedResult.result;
            }
            
            // 执行实际渲染
            const result = await this._doRender(template, processedData, renderOptions);
            
            // 后处理结果
            const finalResult = await this._postprocessResult(result, renderOptions);
            
            // 缓存结果
            if (this.renderConfig.enableCache) {
                this._cacheResult(cacheKey, finalResult);
                this.cacheStats.misses++;
                this.cacheStats.total++;
            }
            
            // 更新统计信息
            this._updateStats(startTime);
            
            // 触发渲染完成事件
            this.emit(EventTypes.RENDERER.RENDER_COMPLETE, {
                renderer: this.name,
                template: template.name,
                renderTime: Date.now() - startTime,
                timestamp: new Date()
            });
            
            return finalResult;
            
        } catch (error) {
            // 更新错误统计
            this.state.errorCount++;
            
            // 触发渲染错误事件
            this.emit(EventTypes.RENDERER.RENDER_ERROR, {
                renderer: this.name,
                template: template?.name || 'unknown',
                error: error.message,
                timestamp: new Date()
            });
            
            throw new Error(`渲染失败: ${error.message}`);
            
        } finally {
            // 重置渲染状态
            this.state.isRendering = false;
            this.state.lastRenderTime = new Date();
        }
    }

    /**
     * 预处理数据 - 对输入数据进行预处理和验证
     * @param {Object} data - 原始数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 处理后的数据
     * @protected
     */
    async _preprocessData(data, options) {
        // 深拷贝数据以避免修改原始对象
        const processedData = JSON.parse(JSON.stringify(data));
        
        // 添加渲染时间戳
        processedData._renderTimestamp = new Date();
        processedData._renderOptions = options;
        
        return processedData;
    }

    /**
     * 执行实际渲染 - 子类必须实现的抽象方法
     * @param {Object} template - 模板对象
     * @param {Object} data - 预处理后的数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 渲染结果
     * @protected
     * @abstract
     */
    async _doRender(template, data, options) {
        throw new Error('_doRender 方法必须在子类中实现');
    }

    /**
     * 后处理结果 - 对渲染结果进行后处理
     * @param {Object} result - 原始渲染结果
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 处理后的结果
     * @protected
     */
    async _postprocessResult(result, options) {
        // 添加元数据
        const finalResult = {
            content: result,
            metadata: {
                renderer: this.name,
                type: this.type,
                outputFormat: options.outputFormat,
                timestamp: new Date(),
                version: this.version
            }
        };
        
        return finalResult;
    }

    /**
     * 生成缓存键 - 为渲染结果生成唯一的缓存键
     * @param {Object} template - 模板对象
     * @param {Object} data - 数据对象
     * @param {Object} options - 渲染选项
     * @returns {string} 缓存键
     * @private
     */
    _generateCacheKey(template, data, options) {
        const keyComponents = [
            this.name,
            template.name,
            template.version,
            JSON.stringify(data),
            JSON.stringify(options)
        ];
        
        // 使用简单的哈希算法生成键
        const keyString = keyComponents.join('|');
        let hash = 0;
        for (let i = 0; i < keyString.length; i++) {
            const char = keyString.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        
        return `${this.name}_${Math.abs(hash)}_${Date.now()}`;
    }

    /**
     * 缓存结果 - 将渲染结果存储到缓存中
     * @param {string} key - 缓存键
     * @param {Object} result - 渲染结果
     * @private
     */
    _cacheResult(key, result) {
        // 检查缓存大小限制
        if (this.cache.size >= this.renderConfig.maxCacheSize) {
            // 删除最老的缓存项
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        
        this.cache.set(key, {
            result: result,
            timestamp: Date.now()
        });
    }

    /**
     * 更新统计信息 - 更新渲染性能统计
     * @param {number} startTime - 开始时间
     * @private
     */
    _updateStats(startTime) {
        const renderTime = Date.now() - startTime;
        
        this.state.renderCount++;
        this.performanceStats.totalRenderTime += renderTime;
        this.performanceStats.averageRenderTime = 
            this.performanceStats.totalRenderTime / this.state.renderCount;
        this.performanceStats.minRenderTime = 
            Math.min(this.performanceStats.minRenderTime, renderTime);
        this.performanceStats.maxRenderTime = 
            Math.max(this.performanceStats.maxRenderTime, renderTime);
        
        // 保留最近的渲染历史
        this.performanceStats.renderHistory.push({
            timestamp: new Date(),
            renderTime: renderTime
        });
        
        // 限制历史记录数量
        if (this.performanceStats.renderHistory.length > 100) {
            this.performanceStats.renderHistory.shift();
        }
    }

    /**
     * 清理缓存 - 手动清理渲染器缓存
     */
    clearCache() {
        this.cache.clear();
        this.cacheStats = { hits: 0, misses: 0, total: 0 };
        
        this.emit(EventTypes.RENDERER.CACHE_CLEARED, {
            renderer: this.name,
            timestamp: new Date()
        });
    }

    /**
     * 获取统计信息 - 返回渲染器的详细统计数据
     * @returns {Object} 统计信息对象
     */
    getStats() {
        return {
            name: this.name,
            type: this.type,
            state: { ...this.state },
            cache: {
                size: this.cache.size,
                maxSize: this.renderConfig.maxCacheSize,
                stats: { ...this.cacheStats }
            },
            performance: { ...this.performanceStats },
            config: { ...this.renderConfig }
        };
    }

    /**
     * 销毁渲染器 - 清理资源并销毁渲染器实例
     */
    destroy() {
        // 清理缓存
        this.clearCache();
        
        // 移除所有事件监听器
        this.removeAllListeners();
        
        // 重置状态
        this.state.isInitialized = false;
        this.state.isRendering = false;
        
        // 触发销毁事件
        this.emit(EventTypes.RENDERER.DESTROYED, {
            renderer: this.name,
            timestamp: new Date()
        });
    }
}
// #endregion

// #region 渲染器注册表类定义
/**
 * @class RendererRegistry - 渲染器注册表类
 * @description 管理所有已注册的渲染器实例
 */
export class RendererRegistry extends EventEmitter {
    /**
     * 构造函数 - 初始化渲染器注册表
     */
    constructor() {
        super();
        
        this.renderers = new Map(); // 存储注册的渲染器
        this.metadata = {
            created: new Date(),
            totalRegistrations: 0,
            lastRegistration: null
        };
    }

    /**
     * 注册渲染器 - 将渲染器添加到注册表
     * @param {BaseRenderer} renderer - 要注册的渲染器实例
     */
    register(renderer) {
        // 验证渲染器
        if (!(renderer instanceof BaseRenderer)) {
            throw new Error('只能注册 BaseRenderer 的实例');
        }
        
        // 检查名称是否已存在
        if (this.renderers.has(renderer.name)) {
            throw new Error(`渲染器名称 "${renderer.name}" 已存在`);
        }
        
        // 注册渲染器
        this.renderers.set(renderer.name, renderer);
        this.metadata.totalRegistrations++;
        this.metadata.lastRegistration = new Date();
        
        // 触发注册事件
        this.emit(EventTypes.RENDERER.REGISTERED, {
            renderer: renderer.name,
            type: renderer.type,
            timestamp: new Date()
        });
    }

    /**
     * 获取渲染器 - 通过名称获取渲染器实例
     * @param {string} name - 渲染器名称
     * @returns {BaseRenderer|null} 渲染器实例
     */
    get(name) {
        return this.renderers.get(name) || null;
    }

    /**
     * 检查渲染器是否存在 - 检查指定名称的渲染器是否已注册
     * @param {string} name - 渲染器名称
     * @returns {boolean} 是否存在
     */
    has(name) {
        return this.renderers.has(name);
    }

    /**
     * 注销渲染器 - 从注册表中移除渲染器
     * @param {string} name - 渲染器名称
     * @returns {boolean} 是否成功注销
     */
    unregister(name) {
        const renderer = this.renderers.get(name);
        if (!renderer) {
            return false;
        }
        
        // 销毁渲染器
        renderer.destroy();
        
        // 从注册表中移除
        this.renderers.delete(name);
        
        // 触发注销事件
        this.emit(EventTypes.RENDERER.UNREGISTERED, {
            renderer: name,
            timestamp: new Date()
        });
        
        return true;
    }

    /**
     * 获取所有渲染器名称 - 返回已注册的所有渲染器名称
     * @returns {Array<string>} 渲染器名称数组
     */
    getNames() {
        return Array.from(this.renderers.keys());
    }

    /**
     * 获取所有渲染器 - 返回所有已注册的渲染器实例
     * @returns {Array<BaseRenderer>} 渲染器实例数组
     */
    getAll() {
        return Array.from(this.renderers.values());
    }

    /**
     * 按类型获取渲染器 - 根据渲染器类型筛选
     * @param {string} type - 渲染器类型
     * @returns {Array<BaseRenderer>} 指定类型的渲染器数组
     */
    getByType(type) {
        return this.getAll().filter(renderer => renderer.type === type);
    }

    /**
     * 获取统计信息 - 返回注册表的统计数据
     * @returns {Object} 统计信息对象
     */
    getStats() {
        const typeDistribution = {};
        const rendererStats = [];
        
        for (const renderer of this.renderers.values()) {
            // 统计类型分布
            typeDistribution[renderer.type] = (typeDistribution[renderer.type] || 0) + 1;
            
            // 收集渲染器统计
            rendererStats.push(renderer.getStats());
        }
        
        return {
            total: this.renderers.size,
            typeDistribution,
            metadata: { ...this.metadata },
            renderers: rendererStats
        };
    }

    /**
     * 清空注册表 - 移除所有已注册的渲染器
     */
    clear() {
        // 销毁所有渲染器
        for (const renderer of this.renderers.values()) {
            renderer.destroy();
        }
        
        // 清空注册表
        this.renderers.clear();
        
        // 触发清空事件
        this.emit(EventTypes.RENDERER.REGISTRY_CLEARED, {
            timestamp: new Date()
        });
    }
}
// #endregion

// #region 渲染引擎类定义
/**
 * @class RenderingEngine - 渲染引擎类
 * @description 统一的渲染入口和流程控制，管理多个渲染器
 */
export class RenderingEngine extends EventEmitter {
    /**
     * 构造函数 - 初始化渲染引擎
     * @param {RendererRegistry} registry - 渲染器注册表实例
     */
    constructor(registry = defaultRegistry) {
        super();
        
        this.registry = registry;
        this.defaultRenderer = null;
        this.renderQueue = [];
        this.isProcessingQueue = false;
        
        // 渲染引擎配置
        this.config = {
            enableQueue: true, // 是否启用渲染队列
            maxConcurrent: 3, // 最大并发渲染数
            queueTimeout: 30000, // 队列超时时间
            enableFallback: true // 是否启用降级渲染
        };
    }

    /**
     * 设置默认渲染器 - 设置当没有指定渲染器时使用的默认渲染器
     * @param {string} rendererName - 渲染器名称
     */
    setDefaultRenderer(rendererName) {
        if (!this.registry.has(rendererName)) {
            throw new Error(`渲染器 "${rendererName}" 不存在`);
        }
        
        this.defaultRenderer = rendererName;
    }

    /**
     * 渲染 - 使用指定的渲染器进行渲染
     * @param {Object} template - 模板对象
     * @param {Object} data - 渲染数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 渲染结果
     */
    async render(template, data, options = {}) {
        const rendererName = options.renderer || this.defaultRenderer;
        
        if (!rendererName) {
            throw new Error('未指定渲染器，且没有设置默认渲染器');
        }
        
        const renderer = this.registry.get(rendererName);
        if (!renderer) {
            throw new Error(`渲染器 "${rendererName}" 不存在`);
        }
        
        // 如果启用队列，则添加到队列中
        if (this.config.enableQueue) {
            return this._addToQueue(renderer, template, data, options);
        }
        
        // 直接渲染
        return renderer.render(template, data, options);
    }

    /**
     * 添加到队列 - 将渲染任务添加到队列中
     * @param {BaseRenderer} renderer - 渲染器实例
     * @param {Object} template - 模板对象
     * @param {Object} data - 渲染数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 渲染结果
     * @private
     */
    async _addToQueue(renderer, template, data, options) {
        return new Promise((resolve, reject) => {
            const task = {
                id: Date.now() + Math.random(),
                renderer,
                template,
                data,
                options,
                resolve,
                reject,
                timestamp: new Date()
            };
            
            this.renderQueue.push(task);
            
            // 处理队列
            this._processQueue();
            
            // 设置超时
            setTimeout(() => {
                const index = this.renderQueue.findIndex(t => t.id === task.id);
                if (index !== -1) {
                    this.renderQueue.splice(index, 1);
                    reject(new Error('渲染任务超时'));
                }
            }, this.config.queueTimeout);
        });
    }

    /**
     * 处理队列 - 处理渲染队列中的任务
     * @private
     */
    async _processQueue() {
        if (this.isProcessingQueue || this.renderQueue.length === 0) {
            return;
        }
        
        this.isProcessingQueue = true;
        
        while (this.renderQueue.length > 0) {
            const task = this.renderQueue.shift();
            
            try {
                const result = await task.renderer.render(task.template, task.data, task.options);
                task.resolve(result);
            } catch (error) {
                task.reject(error);
            }
        }
        
        this.isProcessingQueue = false;
    }
}
// #endregion

// #region 默认实例导出
// 创建默认的渲染器注册表
export const defaultRegistry = new RendererRegistry();

// 创建默认的渲染引擎
export const defaultRenderingEngine = new RenderingEngine(defaultRegistry);
// #endregion 