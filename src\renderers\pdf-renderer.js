/**
 * @file PDF渲染器 - 实现PDF格式的文档渲染功能
 * <AUTHOR> Team
 * @description 
 * 这个文件定义了PDF渲染器，专门用于将模板渲染成PDF格式，包括：
 * - PDFRenderer 类，继承自 BaseRenderer
 * - PDF专用的渲染逻辑和页面设置
 * - 支持多种PDF生成方式（Puppeteer、jsPDF、html2pdf等）
 * - PDF质量控制和优化功能
 */

// #region 导入依赖模块
import { UnifiedRenderer } from './unified-renderer.js';
import { DocumentModel } from '../core/rendering/document-model.js';
import { StringUtils } from '../core/utils/string-utils.js';
import { DateUtils } from '../core/utils/date-utils.js';
import { EventTypes } from '../core/events/event-types.js';
// #endregion

// #region PDF渲染器类定义
/**
 * @class PDFRenderer - PDF渲染器类
 * @description 专门用于渲染PDF格式文档的渲染器类，继承自UnifiedRenderer
 */
export class PDFRenderer extends UnifiedRenderer {
    /**
     * 构造函数 - 初始化PDF渲染器实例
     * @param {Object} config - 渲染器配置对象
     */
    constructor(config = {}) {
        // 设置PDF渲染器的默认配置
        const defaultConfig = {
            name: config.name || 'pdf-renderer',
            type: 'pdf',
            version: config.version || '2.0.0',
            description: 'PDF格式文档渲染器 - 统一渲染架构',
            
            // PDF渲染器专用配置
            renderConfig: {
                outputFormat: 'pdf',
                
                // PDF生成引擎配置
                engine: 'html2pdf', // html2pdf, jspdf, puppeteer
                
                // 页面配置
                format: 'A4', // A4, A3, Letter, Legal等
                orientation: 'portrait', // portrait, landscape
                margin: {
                    top: '20mm',
                    right: '20mm',
                    bottom: '20mm',
                    left: '20mm'
                },
                
                // 质量配置
                quality: 'high', // low, medium, high
                dpi: 300, // 分辨率
                compression: true, // 是否压缩
                
                // 页眉页脚配置
                displayHeaderFooter: true,
                headerTemplate: '',
                footerTemplate: '',
                
                // 打印配置
                printBackground: true, // 是否打印背景
                preferCSSPageSize: true, // 是否优先使用CSS页面大小
                
                // 安全配置
                enableProtection: false, // 是否启用保护
                userPassword: '', // 用户密码
                ownerPassword: '', // 所有者密码
                permissions: [], // 权限设置
                
                // 优化配置
                enableOptimization: true, // 是否启用优化
                removeUnusedObjects: true, // 移除未使用对象
                compressStreams: true, // 压缩流
                
                ...config.renderConfig
            },
            
            ...config
        };
        
        // 调用父类构造函数
        super(defaultConfig);
        
        // PDF渲染器专用属性
        // 注意：不再依赖HTMLRenderer，直接使用父类的通用渲染方法
        
        // 支持的页面格式
        this.supportedFormats = ['A4', 'A3', 'A5', 'Letter', 'Legal', 'Tabloid'];
        this.supportedOrientations = ['portrait', 'landscape'];
        this.supportedEngines = ['html2pdf', 'jspdf', 'puppeteer'];
        
        // PDF生成器缓存
        this.generatorCache = new Map();
        
        // PDF生成引擎实例
        this.pdfEngine = null;
        this.engineInitialized = false;
        
        // 初始化PDF引擎
        this._initializePDFEngine();
    }

    /**
     * 获取支持的格式
     * @returns {Array<string>} 支持的格式列表
     */
    getSupportedFormats() {
        return ['pdf'];
    }
    
    /**
     * 获取渲染器特性
     * @returns {Object} 特性对象
     */
    getFeatures() {
        return {
            supportsPagination: true,
            supportsVectorGraphics: true,
            supportsHighDPI: true,
            supportsProtection: true,
            supportsCompression: true,
            supportsBookmarks: true,
            supportsAnnotations: true,
            maxFileSize: '100MB',
            recommendedDPI: 300
        };
    }
    
    /**
     * 执行渲染 - 统一渲染架构方法
     * @param {DocumentModel} documentModel - 文档模型
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 渲染结果
     * @protected
     */
    async _executeRender(documentModel, options = {}) {
        try {
            // 验证文档模型
            if (!(documentModel instanceof DocumentModel)) {
                throw new Error('PDF渲染器需要DocumentModel实例');
            }
            
            // 使用父类的通用处理方法
            const styles = await super._processStyles(documentModel, options);
            const layout = await super._processLayout(documentModel, options);
            const positions = await super._processPositions(documentModel, layout, options);
            
            // 生成HTML内容用于PDF转换
            const htmlContent = await this._generateHTMLForPDF(documentModel, {
                styles,
                layout,
                positions,
                ...options
            });
            
            // 生成PDF
            const pdfBlob = await this._generatePDF(htmlContent, options);
            
            return {
                content: htmlContent,
                blob: pdfBlob,
                format: 'pdf',
                metadata: {
                    pages: await this._getPageCount(pdfBlob),
                    size: pdfBlob.size,
                    engine: this.config.renderConfig.engine
                }
            };
            
        } catch (error) {
            console.error('[PDFRenderer] 渲染失败:', error);
            throw error;
        }
    }
    
    /**
     * 执行实际渲染 - 重写父类方法（保持向后兼容）
     * @param {Object} template - 模板对象
     * @param {Object} data - 预处理后的数据对象
     * @param {Object} options - 渲染选项配置
     * @returns {Promise<Blob>} PDF Blob对象
     * @protected
     */
    async _doRender(template, data, options) {
        try {
            // 首先生成HTML内容
            const htmlContent = await this._generateHTML(template, data, options);
            
            // 根据引擎选择PDF生成方法
            let pdfData;
            switch (options.engine) {
                case 'puppeteer':
                    pdfData = await this._renderWithPuppeteer(htmlContent, options);
                    break;
                case 'jspdf':
                    pdfData = await this._renderWithJsPDF(htmlContent, options);
                    break;
                case 'html2pdf':
                default:
                    pdfData = await this._renderWithHtml2PDF(htmlContent, options);
                    break;
            }
            
            // 后处理PDF
            const processedPDF = await this._postprocessPDF(pdfData, options);
            
            return processedPDF;
            
        } catch (error) {
            throw new Error(`PDF渲染失败: ${error.message}`);
        }
    }

    /**
     * 生成HTML内容 - 使用HTML渲染器生成PDF的HTML源
     * @param {Object} template - 模板对象
     * @param {Object} data - 渲染数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} HTML内容
     * @private
     */
    async _generateHTML(template, data, options) {
        // 配置HTML渲染选项，优化PDF输出
        const htmlOptions = {
            ...options,
            includeCSS: true,
            inlineCSS: true,
            printFriendly: true,
            enableResponsive: false,
            enableInteraction: false,
            removeComments: true,
            customCSS: this._getPDFOptimizedCSS(options)
        };
        
        // 生成基础HTML内容
        const htmlContent = await this._generateBasicHTML(template, data, htmlOptions);
        
        return htmlContent;
    }

    /**
     * 获取PDF优化的CSS - 返回针对PDF优化的CSS样式
     * @param {Object} options - 渲染选项
     * @returns {string} PDF优化CSS
     * @private
     */
    _getPDFOptimizedCSS(options) {
        const pageSize = this._getPageSizeCSS(options.format, options.orientation);
        const margins = this._getMarginCSS(options.margin);
        
        return `
        /* PDF优化样式 */
        @page {
            ${pageSize}
            ${margins}
            -webkit-print-color-adjust: exact;
            color-adjust: exact;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: "Microsoft YaHei", Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.4;
            color: #000;
            background: white;
        }
        
        .document-container {
            margin: 0;
            padding: 0;
            box-shadow: none;
            border: none;
            max-width: none;
            min-height: auto;
            background: white;
        }
        
        /* 避免页面分割 */
        .page-break-avoid {
            page-break-inside: avoid;
            break-inside: avoid;
        }
        
        .page-break-before {
            page-break-before: always;
            break-before: page;
        }
        
        .page-break-after {
            page-break-after: always;
            break-after: page;
        }
        
        /* 表格优化 */
        table {
            page-break-inside: auto;
        }
        
        tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }
        
        thead {
            display: table-header-group;
        }
        
        tfoot {
            display: table-footer-group;
        }
        
        /* 图片优化 */
        img {
            max-width: 100%;
            height: auto;
            page-break-inside: avoid;
        }
        
        /* 文本优化 */
        h1, h2, h3, h4, h5, h6 {
            page-break-after: avoid;
        }
        
        p, div {
            orphans: 3;
            widows: 3;
        }
        
        /* 签名区域优化 */
        .signature-section {
            page-break-inside: avoid;
            margin-top: 20pt;
        }
        
        .signature-line {
            border-bottom: 1pt solid #000;
        }
        `;
    }

    /**
     * 获取页面大小CSS - 根据格式和方向生成CSS
     * @param {string} format - 页面格式
     * @param {string} orientation - 页面方向
     * @returns {string} 页面大小CSS
     * @private
     */
    _getPageSizeCSS(format, orientation) {
        const sizes = {
            'A4': orientation === 'landscape' ? 'size: 297mm 210mm;' : 'size: 210mm 297mm;',
            'A3': orientation === 'landscape' ? 'size: 420mm 297mm;' : 'size: 297mm 420mm;',
            'A5': orientation === 'landscape' ? 'size: 210mm 148mm;' : 'size: 148mm 210mm;',
            'Letter': orientation === 'landscape' ? 'size: 11in 8.5in;' : 'size: 8.5in 11in;',
            'Legal': orientation === 'landscape' ? 'size: 14in 8.5in;' : 'size: 8.5in 14in;'
        };
        
        return sizes[format] || sizes['A4'];
    }

    /**
     * 获取边距CSS - 生成页面边距CSS
     * @param {Object} margin - 边距配置
     * @returns {string} 边距CSS
     * @private
     */
    _getMarginCSS(margin) {
        return `margin: ${margin.top} ${margin.right} ${margin.bottom} ${margin.left};`;
    }

    /**
     * 使用html2pdf渲染 - 使用html2pdf.js库生成PDF
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 渲染选项
     * @returns {Promise<Blob>} PDF Blob数据
     * @private
     */
    async _renderWithHtml2PDF(htmlContent, options) {
        // 检查html2pdf是否可用
        if (typeof window === 'undefined' || !window.html2pdf) {
            throw new Error('html2pdf库未加载，请确保在浏览器环境中使用');
        }
        
        // 配置html2pdf选项
        const html2pdfOptions = {
            margin: this._convertMarginToNumbers(options.margin),
            filename: `document_${Date.now()}.pdf`,
            image: { 
                type: 'jpeg', 
                quality: options.quality === 'high' ? 0.98 : 
                        options.quality === 'medium' ? 0.85 : 0.7 
            },
            html2canvas: { 
                scale: options.dpi / 96, // 将DPI转换为缩放比例
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff'
            },
            jsPDF: { 
                unit: 'mm', 
                format: options.format.toLowerCase(), 
                orientation: options.orientation 
            }
        };
        
        // 创建临时元素
        const tempElement = document.createElement('div');
        tempElement.innerHTML = htmlContent;
        tempElement.style.display = 'none';
        document.body.appendChild(tempElement);
        
        try {
            // 生成PDF
            const pdf = await window.html2pdf()
                .set(html2pdfOptions)
                .from(tempElement)
                .outputPdf('blob');
            
            return pdf;
            
        } finally {
            // 清理临时元素
            document.body.removeChild(tempElement);
        }
    }

    /**
     * 使用jsPDF渲染 - 使用jsPDF库生成PDF
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 渲染选项
     * @returns {Promise<Blob>} PDF Blob数据
     * @private
     */
    async _renderWithJsPDF(htmlContent, options) {
        // 检查jsPDF是否可用
        if (typeof window === 'undefined' || !window.jsPDF) {
            throw new Error('jsPDF库未加载，请确保在浏览器环境中使用');
        }
        
        // 创建jsPDF实例
        const pdf = new window.jsPDF({
            orientation: options.orientation,
            unit: 'mm',
            format: options.format.toLowerCase()
        });
        
        // 解析HTML内容并添加到PDF
        await this._parseHTMLForJsPDF(pdf, htmlContent, options);
        
        // 返回PDF Blob
        return pdf.output('blob');
    }

    /**
     * 解析HTML内容用于jsPDF - 将HTML转换为jsPDF可用的格式
     * @param {Object} pdf - jsPDF实例
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 渲染选项
     * @private
     */
    async _parseHTMLForJsPDF(pdf, htmlContent, options) {
        // 创建临时DOM元素用于解析
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        
        let yPosition = 20; // 当前Y位置
        const margin = this._convertMarginToNumbers(options.margin);
        const pageHeight = this._getPageHeight(options.format) - margin.top - margin.bottom;
        
        // 遍历文档元素
        const elements = doc.body.querySelectorAll('*');
        
        for (const element of elements) {
            const tagName = element.tagName.toLowerCase();
            const text = element.textContent.trim();
            
            if (!text) continue;
            
            // 检查是否需要换页
            if (yPosition > pageHeight - 20) {
                pdf.addPage();
                yPosition = 20;
            }
            
            // 根据元素类型处理
            switch (tagName) {
                case 'h1':
                case 'h2':
                case 'h3':
                    pdf.setFontSize(tagName === 'h1' ? 18 : tagName === 'h2' ? 16 : 14);
                    pdf.setFont(undefined, 'bold');
                    pdf.text(text, margin.left, yPosition);
                    yPosition += 10;
                    break;
                    
                case 'p':
                case 'div':
                    pdf.setFontSize(12);
                    pdf.setFont(undefined, 'normal');
                    const lines = pdf.splitTextToSize(text, 180);
                    pdf.text(lines, margin.left, yPosition);
                    yPosition += lines.length * 5;
                    break;
                    
                case 'table':
                    yPosition = await this._renderTableForJsPDF(pdf, element, margin.left, yPosition, options);
                    break;
            }
            
            yPosition += 5; // 元素间距
        }
    }

    /**
     * 为jsPDF渲染表格 - 将表格元素转换为jsPDF表格
     * @param {Object} pdf - jsPDF实例
     * @param {Element} tableElement - 表格DOM元素
     * @param {number} x - X位置
     * @param {number} y - Y位置
     * @param {Object} options - 渲染选项
     * @returns {Promise<number>} 渲染后的Y位置
     * @private
     */
    async _renderTableForJsPDF(pdf, tableElement, x, y, options) {
        // 这里实现表格渲染逻辑
        // 简化实现，实际应该更详细
        const rows = tableElement.querySelectorAll('tr');
        let currentY = y;
        
        for (const row of rows) {
            const cells = row.querySelectorAll('td, th');
            let cellX = x;
            
            for (const cell of cells) {
                const text = cell.textContent.trim();
                if (text) {
                    pdf.text(text, cellX, currentY);
                    cellX += 40; // 简单的列宽
                }
            }
            
            currentY += 8; // 行高
        }
        
        return currentY;
    }

    /**
     * 使用Puppeteer渲染 - 使用Puppeteer生成PDF（服务器端）
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 渲染选项
     * @returns {Promise<Buffer>} PDF Buffer数据
     * @private
     */
    async _renderWithPuppeteer(htmlContent, options) {
        // 注意：Puppeteer通常在Node.js环境中使用
        // 这里提供接口，实际实现需要在服务器端
        
        throw new Error('Puppeteer渲染需要在服务器端实现');
        
        // 服务器端实现示例：
        /*
        const puppeteer = require('puppeteer');
        
        const browser = await puppeteer.launch();
        const page = await browser.newPage();
        
        await page.setContent(htmlContent);
        
        const pdfBuffer = await page.pdf({
            format: options.format,
            orientation: options.orientation,
            margin: options.margin,
            printBackground: options.printBackground,
            displayHeaderFooter: options.displayHeaderFooter,
            headerTemplate: options.headerTemplate,
            footerTemplate: options.footerTemplate
        });
        
        await browser.close();
        
        return pdfBuffer;
        */
    }

    /**
     * 后处理PDF - 对生成的PDF进行优化和处理
     * @param {Blob|ArrayBuffer|Buffer} pdfData - 原始PDF数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<Blob|ArrayBuffer|Buffer>} 处理后的PDF数据
     * @private
     */
    async _postprocessPDF(pdfData, options) {
        // 如果启用了优化
        if (options.enableOptimization) {
            // 这里可以添加PDF优化逻辑
            // 例如压缩、移除未使用对象等
        }
        
        // 如果启用了保护
        if (options.enableProtection) {
            // 这里可以添加PDF保护逻辑
            // 例如设置密码、限制权限等
        }
        
        return pdfData;
    }

    /**
     * 转换边距为数字 - 将边距字符串转换为数字
     * @param {Object} margin - 边距配置
     * @returns {Object} 数字边距
     * @private
     */
    _convertMarginToNumbers(margin) {
        const convertUnit = (value) => {
            if (typeof value === 'number') return value;
            if (typeof value === 'string') {
                const num = parseFloat(value);
                if (value.includes('mm')) return num;
                if (value.includes('cm')) return num * 10;
                if (value.includes('in')) return num * 25.4;
                if (value.includes('pt')) return num * 0.353;
                return num; // 假设是mm
            }
            return 20; // 默认值
        };
        
        return {
            top: convertUnit(margin.top),
            right: convertUnit(margin.right),
            bottom: convertUnit(margin.bottom),
            left: convertUnit(margin.left)
        };
    }

    /**
     * 获取页面高度 - 根据格式获取页面高度（mm）
     * @param {string} format - 页面格式
     * @returns {number} 页面高度
     * @private
     */
    _getPageHeight(format) {
        const heights = {
            'A4': 297,
            'A3': 420,
            'A5': 210,
            'Letter': 279.4,
            'Legal': 355.6
        };
        
        return heights[format] || heights['A4'];
    }

    /**
     * 设置PDF选项 - 配置PDF生成参数
     * @param {Object} options - PDF选项
     */
    setPDFOptions(options) {
        this.renderConfig = {
            ...this.renderConfig,
            ...options
        };
    }

    /**
     * 获取支持的格式列表 - 返回PDF渲染器支持的所有格式
     * @returns {Array<Object>} 格式信息数组
     */
    getSupportedFormats() {
        return [
            { id: 'A4', name: 'A4', size: '210 × 297mm' },
            { id: 'A3', name: 'A3', size: '297 × 420mm' },
            { id: 'A5', name: 'A5', size: '148 × 210mm' },
            { id: 'Letter', name: 'Letter', size: '8.5 × 11in' },
            { id: 'Legal', name: 'Legal', size: '8.5 × 14in' }
        ];
    }

    /**
     * 获取支持的引擎列表 - 返回PDF渲染器支持的所有引擎
     * @returns {Array<Object>} 引擎信息数组
     */
    getSupportedEngines() {
        return [
            {
                id: 'html2pdf',
                name: 'html2pdf.js',
                description: '基于html2canvas和jsPDF的PDF生成器',
                environment: 'browser'
            },
            {
                id: 'jspdf',
                name: 'jsPDF',
                description: '纯JavaScript PDF生成器',
                environment: 'browser'
            },
            {
                id: 'puppeteer',
                name: 'Puppeteer',
                description: '基于Chrome的PDF生成器',
                environment: 'server'
            }
        ];
    }

    /**
     * 验证PDF配置 - 检查PDF配置的有效性
     * @param {Object} options - PDF配置选项
     * @returns {Object} 验证结果
     */
    validatePDFConfig(options) {
        const errors = [];
        const warnings = [];
        
        // 检查页面格式
        if (!this.supportedFormats.includes(options.format)) {
            errors.push(`不支持的页面格式: ${options.format}`);
        }
        
        // 检查页面方向
        if (!this.supportedOrientations.includes(options.orientation)) {
            errors.push(`不支持的页面方向: ${options.orientation}`);
        }
        
        // 检查渲染引擎
        if (!this.supportedEngines.includes(options.engine)) {
            errors.push(`不支持的渲染引擎: ${options.engine}`);
        }
        
        // 检查边距
        if (!options.margin || typeof options.margin !== 'object') {
            warnings.push('未设置页面边距，将使用默认值');
        }
        
        // 检查DPI
        if (options.dpi && (options.dpi < 72 || options.dpi > 600)) {
            warnings.push('DPI值超出推荐范围（72-600）');
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * 估算PDF大小 - 根据内容估算生成的PDF文件大小
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 渲染选项
     * @returns {Object} 大小估算信息
     */
    estimatePDFSize(htmlContent, options) {
        // 简单的大小估算逻辑
        const baseSize = 50 * 1024; // 50KB基础大小
        const contentSize = htmlContent.length * 0.1; // 内容大小因子
        const qualityFactor = options.quality === 'high' ? 2.5 : 
                             options.quality === 'medium' ? 1.5 : 1.0;
        const compressionFactor = options.compression ? 0.7 : 1.0;
        
        const estimatedSize = (baseSize + contentSize) * qualityFactor * compressionFactor;
        
        return {
            estimatedBytes: Math.round(estimatedSize),
            estimatedKB: Math.round(estimatedSize / 1024),
            estimatedMB: Math.round(estimatedSize / (1024 * 1024) * 100) / 100
        };
    }

    /**
     * 初始化PDF引擎
     * @private
     */
    _initializePDFEngine() {
        // PDF引擎初始化逻辑
        this.engineInitialized = true;
    }
    
    /**
     * 生成基础HTML内容
     * @param {Object} template - 模板
     * @param {Object} data - 数据
     * @param {Object} options - 选项
     * @returns {Promise<string>} HTML内容
     * @private
     */
    async _generateBasicHTML(template, data, options) {
        // 基础HTML生成逻辑
        return `<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>PDF Document</title>
    <style>${this._getPDFOptimizedCSS(options)}</style>
</head>
<body>
    ${data.content || ''}
</body>
</html>`;
    }
    
    /**
     * 生成预览HTML
     * @param {Object} template - 模板
     * @param {Object} data - 数据
     * @param {Object} options - 选项
     * @returns {Promise<string>} 预览HTML
     * @private
     */
    async _generatePreviewHTML(template, data, options) {
        return await this._generateBasicHTML(template, data, options);
    }
    
    /**
     * 生成基础HTML内容
     * @param {DocumentModel} documentModel - 文档模型
     * @param {Object} options - 选项
     * @returns {Promise<string>} HTML内容
     * @private
     */
    async _generateBasicHTMLContent(documentModel, options) {
        const content = await documentModel.renderContent();
        return `<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>${documentModel.title || 'PDF Document'}</title>
    <style>${this._getPDFOptimizedCSS(options)}</style>
</head>
<body>
    ${content}
</body>
</html>`;
    }
    
    /**
     * 使用html2pdf生成PDF
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 选项
     * @returns {Promise<Blob>} PDF Blob
     * @private
     */
    async _generatePDFWithHtml2pdf(htmlContent, options) {
        return await this._renderWithHtml2PDF(htmlContent, options);
    }
    
    /**
     * 使用Puppeteer生成PDF
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 选项
     * @returns {Promise<Blob>} PDF Blob
     * @private
     */
    async _generatePDFWithPuppeteer(htmlContent, options) {
        return await this._renderWithPuppeteer(htmlContent, options);
    }
    
    /**
     * 使用jsPDF生成PDF
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 选项
     * @returns {Promise<Blob>} PDF Blob
     * @private
     */
    async _generatePDFWithJsPDF(htmlContent, options) {
        return await this._renderWithJsPDF(htmlContent, options);
    }
    
    /**
     * 创建PDF预览 - 生成用于预览的PDF缩略图
     * @param {Object} template - 模板对象
     * @param {Object} data - 渲染数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} 预览图片的base64数据
     */
    async createPDFPreview(template, data, options = {}) {
        // 生成HTML预览
        const htmlPreview = await this._generatePreviewHTML(template, data, options);
        
        // 如果在浏览器环境中，可以使用html2canvas生成预览图
        if (typeof window !== 'undefined' && window.html2canvas) {
            const tempElement = document.createElement('div');
            tempElement.innerHTML = htmlPreview;
            tempElement.style.width = '210mm';
            tempElement.style.background = 'white';
            tempElement.style.position = 'absolute';
            tempElement.style.left = '-9999px';
            document.body.appendChild(tempElement);
            
            try {
                const canvas = await window.html2canvas(tempElement, {
                    scale: 0.5,
                    useCORS: true,
                    backgroundColor: '#ffffff'
                });
                
                return canvas.toDataURL('image/jpeg', 0.8);
                
            } finally {
                document.body.removeChild(tempElement);
            }
        }
        
        // 如果没有html2canvas，返回空预览
        return null;
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建PDF渲染器实例 - 工厂函数，方便创建PDF渲染器
 * @param {Object} config - 渲染器配置
 * @returns {PDFRenderer} PDF渲染器实例
 */
export function createPDFRenderer(config = {}) {
    return new PDFRenderer(config);
}

/**
 * 创建预设PDF渲染器 - 创建具有预定义配置的PDF渲染器
 * @param {string} preset - 预设名称 ('default', 'high-quality', 'print', 'web')
 * @param {Object} config - 额外配置
 * @returns {PDFRenderer} PDF渲染器实例
 */
export function createPresetPDFRenderer(preset = 'default', config = {}) {
    const presetConfigs = {
        default: {
            renderConfig: {
                format: 'A4',
                orientation: 'portrait',
                quality: 'medium',
                dpi: 150,
                compression: true
            }
        },
        
        'high-quality': {
            renderConfig: {
                format: 'A4',
                orientation: 'portrait',
                quality: 'high',
                dpi: 300,
                compression: false,
                enableOptimization: false
            }
        },
        
        print: {
            renderConfig: {
                format: 'A4',
                orientation: 'portrait',
                quality: 'high',
                dpi: 300,
                compression: true,
                printBackground: true,
                margin: {
                    top: '15mm',
                    right: '15mm',
                    bottom: '15mm',
                    left: '15mm'
                }
            }
        },
        
        web: {
            renderConfig: {
                format: 'A4',
                orientation: 'portrait',
                quality: 'medium',
                dpi: 150,
                compression: true,
                enableOptimization: true
            }
        }
    };
    
    const presetConfig = presetConfigs[preset] || presetConfigs.default;
    const mergedConfig = {
        ...presetConfig,
        ...config,
        renderConfig: {
            ...presetConfig.renderConfig,
            ...config.renderConfig
        }
    };
    
    return new PDFRenderer(mergedConfig);
}
// #endregion
    
    /**
     * 生成PDF专用HTML内容
     * @param {DocumentModel} documentModel - 文档模型
     * @param {Object} options - 选项
     * @returns {Promise<string>} HTML内容
     * @private
     */
    async _generateHTMLForPDF(documentModel, options) {
        try {
            // 生成基础HTML内容
            let htmlContent = await this._generateBasicHTMLContent(documentModel, {
                ...options,
                target: 'pdf',
                printFriendly: true,
                inlineCSS: true
            });
            
            // 添加PDF专用样式和脚本
            htmlContent = this._addPDFOptimizations(htmlContent, options);
            
            return htmlContent;
        } catch (error) {
            console.error('[PDFRenderer] HTML生成失败:', error);
            throw error;
        }
    }
    
    /**
     * 生成PDF
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 选项
     * @returns {Promise<Blob>} PDF Blob
     * @private
     */
    async _generatePDF(htmlContent, options) {
        try {
            const engine = options.engine || this.config.renderConfig.engine;
            
            switch (engine) {
                case 'html2pdf':
                    return await this._generatePDFWithHtml2pdf(htmlContent, options);
                case 'puppeteer':
                    return await this._generatePDFWithPuppeteer(htmlContent, options);
                case 'jspdf':
                    return await this._generatePDFWithJsPDF(htmlContent, options);
                default:
                    return await this._generatePDFWithHtml2pdf(htmlContent, options);
            }
        } catch (error) {
            console.error('[PDFRenderer] PDF生成失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取PDF专用样式
     * @param {Object} options - 选项
     * @returns {string} CSS样式
     * @private
     */
    _getPDFSpecificStyles(options) {
        return `
            @page {
                size: ${options.format || 'A4'} ${options.orientation || 'portrait'};
                margin: ${this._formatMargin(options.margin)};
            }
            
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                font-size: 12pt;
                line-height: 1.4;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            .no-break {
                page-break-inside: avoid;
            }
        `;
    }
    
    /**
     * 获取默认PDF样式
     * @param {Object} options - 选项
     * @returns {Object} 样式对象
     * @private
     */
    _getDefaultPDFStyles(options) {
        return {
            base: this._getPDFSpecificStyles(options),
            theme: '',
            components: '',
            custom: options.customCSS || ''
        };
    }
    
    /**
     * 获取默认位置
     * @param {Object} options - 选项
     * @returns {Object} 位置对象
     * @private
     */
    _getDefaultPositions(options) {
        return {
            elements: [],
            pages: 1,
            coordinates: 'absolute'
        };
    }
    
    /**
     * 计算页面尺寸
     * @param {Object} layout - 布局对象
     * @returns {Object} 尺寸对象
     * @private
     */
    _calculatePageDimensions(layout) {
        const pageSizes = {
            'A4': { width: 210, height: 297 },
            'A3': { width: 297, height: 420 },
            'Letter': { width: 216, height: 279 },
            'Legal': { width: 216, height: 356 }
        };
        
        const size = pageSizes[layout.pageSize] || pageSizes['A4'];
        
        if (layout.orientation === 'landscape') {
            return { width: size.height, height: size.width };
        }
        
        return size;
    }
    
    /**
     * 添加PDF优化
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 选项
     * @returns {string} 优化后的HTML
     * @private
     */
    _addPDFOptimizations(htmlContent, options) {
        // 添加PDF专用meta标签
        const metaTags = `
            <meta name="pdf-engine" content="${options.engine || 'html2pdf'}">
            <meta name="pdf-format" content="${options.format || 'A4'}">
            <meta name="pdf-orientation" content="${options.orientation || 'portrait'}">
        `;
        
        // 在head标签中插入meta标签
        htmlContent = htmlContent.replace('</head>', `${metaTags}</head>`);
        
        return htmlContent;
    }
    
    /**
     * 格式化边距
     * @param {Object|string} margin - 边距
     * @returns {string} 格式化的边距
     * @private
     */
    _formatMargin(margin) {
        if (typeof margin === 'string') {
            return margin;
        }
        
        if (typeof margin === 'object') {
            return `${margin.top || '20mm'} ${margin.right || '20mm'} ${margin.bottom || '20mm'} ${margin.left || '20mm'}`;
        }
        
        return '20mm';
    }
    
    /**
     * 获取页面数量
     * @param {Blob} pdfBlob - PDF Blob
     * @returns {Promise<number>} 页面数量
     * @private
     */
    async _getPageCount(pdfBlob) {
        try {
            // 这里需要实现PDF页面数量检测
            // 可以使用pdf-lib或其他PDF解析库
            return 1; // 临时返回1
        } catch (error) {
            console.warn('[PDFRenderer] 无法获取页面数量:', error);
            return 1;
        }
    }

    /**
     * 创建PDF预览 - 生成用于预览的PDF缩略图
     * @param {Object} template - 模板对象
     * @param {Object} data - 渲染数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<string>} 预览图片的base64数据
     */
    async createPDFPreview(template, data, options = {}) {
        // 生成HTML预览
        const htmlPreview = await this._generatePreviewHTML(template, data, options);
        
        // 如果在浏览器环境中，可以使用html2canvas生成预览图
        if (typeof window !== 'undefined' && window.html2canvas) {
            const tempElement = document.createElement('div');
            tempElement.innerHTML = htmlPreview;
            tempElement.style.width = '210mm';
            tempElement.style.background = 'white';
            tempElement.style.position = 'absolute';
            tempElement.style.left = '-9999px';
            document.body.appendChild(tempElement);
            
            try {
                const canvas = await window.html2canvas(tempElement, {
                    scale: 0.5,
                    useCORS: true,
                    backgroundColor: '#ffffff'
                });
                
                return canvas.toDataURL('image/jpeg', 0.8);
                
            } finally {
                document.body.removeChild(tempElement);
            }
        }
        
        // 如果没有html2canvas，返回空预览
        return null;
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建PDF渲染器实例 - 工厂函数，方便创建PDF渲染器
 * @param {Object} config - 渲染器配置
 * @returns {PDFRenderer} PDF渲染器实例
 */
export function createPDFRenderer(config = {}) {
    return new PDFRenderer(config);
}

/**
 * 创建预设PDF渲染器 - 创建具有预定义配置的PDF渲染器
 * @param {string} preset - 预设名称 ('default', 'high-quality', 'print', 'web')
 * @param {Object} config - 额外配置
 * @returns {PDFRenderer} PDF渲染器实例
 */
export function createPresetPDFRenderer(preset = 'default', config = {}) {
    const presetConfigs = {
        default: {
            renderConfig: {
                format: 'A4',
                orientation: 'portrait',
                quality: 'medium',
                dpi: 150,
                compression: true
            }
        },
        
        'high-quality': {
            renderConfig: {
                format: 'A4',
                orientation: 'portrait',
                quality: 'high',
                dpi: 300,
                compression: false,
                enableOptimization: false
            }
        },
        
        print: {
            renderConfig: {
                format: 'A4',
                orientation: 'portrait',
                quality: 'high',
                dpi: 300,
                compression: true,
                printBackground: true,
                margin: {
                    top: '15mm',
                    right: '15mm',
                    bottom: '15mm',
                    left: '15mm'
                }
            }
        },
        
        web: {
            renderConfig: {
                format: 'A4',
                orientation: 'portrait',
                quality: 'medium',
                dpi: 150,
                compression: true,
                enableOptimization: true
            }
        }
    };
    
    const presetConfig = presetConfigs[preset] || presetConfigs.default;
    const mergedConfig = {
        ...presetConfig,
        ...config,
        renderConfig: {
            ...presetConfig.renderConfig,
            ...config.renderConfig
        }
    };
    
    return new PDFRenderer(mergedConfig);
}
// #endregion