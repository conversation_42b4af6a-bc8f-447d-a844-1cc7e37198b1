/**
 * @file 打印渲染器 - 实现打印优化的文档渲染功能
 * <AUTHOR> Team
 * @description 
 * 这个文件定义了打印渲染器，专门用于优化文档的打印输出，包括：
 * - PrintRenderer 类，继承自 HTMLRenderer
 * - 打印专用的样式优化和页面设置
 * - 打印预览和打印控制功能
 * - 支持多种打印选项和页面分割控制
 */

// #region 导入依赖模块
import { UnifiedRenderer } from './unified-renderer.js';
import { HTMLRenderer } from './html-renderer.js';
import { DocumentModel } from '../core/rendering/document-model.js';
import { StringUtils } from '../core/utils/string-utils.js';
import { DateUtils } from '../core/utils/date-utils.js';
import { EventTypes } from '../core/events/event-types.js';
// #endregion

// #region 打印渲染器类定义
/**
 * @class PrintRenderer - 打印渲染器类
 * @description 专门用于打印优化的渲染器类，继承自UnifiedRenderer
 */
export class PrintRenderer extends UnifiedRenderer {
    /**
     * 构造函数 - 初始化打印渲染器实例
     * @param {Object} config - 渲染器配置对象
     */
    constructor(config = {}) {
        // 设置打印渲染器的默认配置
        const defaultConfig = {
            name: config.name || 'print-renderer',
            type: 'print',
            version: config.version || '2.0.0',
            description: '打印优化文档渲染器 - 统一渲染架构',
            
            // 打印渲染器专用配置
            renderConfig: {
                outputFormat: 'html',
                
                // 打印专用配置
                printOptimized: true, // 是否启用打印优化
                removeBackground: false, // 是否移除背景
                enablePageBreaks: true, // 是否启用分页
                scaleToFit: true, // 是否缩放适应页面
                
                // 页面配置
                pageSize: 'A4', // 页面大小
                orientation: 'portrait', // 页面方向
                margins: '20mm', // 页面边距
                
                // 样式配置
                includeCSS: true,
                inlineCSS: true,
                printFriendly: true,
                enableResponsive: false,
                enableInteraction: false,
                customCSS: '',
                
                // 打印控制
                showPrintDialog: true, // 是否显示打印对话框
                autoprint: false, // 是否自动打印
                printPreview: true, // 是否显示打印预览
                
                ...config.renderConfig
            },
            
            ...config
        };
        
        // 调用父类构造函数
        super(defaultConfig);
        
        // 打印渲染器专用属性
        this.htmlRenderer = new HTMLRenderer({
            name: 'print-html-renderer',
            styleManager: this.styleManager,
            positionManager: this.positionManager,
            renderConfig: {
                includeCSS: true,
                inlineCSS: true,
                printFriendly: true,
                enableResponsive: false,
                enableInteraction: false
            }
        });
        
        this.printCSS = this._getPrintOptimizedCSS();
        this.supportedPageSizes = ['A4', 'A3', 'A5', 'Letter', 'Legal'];
        this.supportedOrientations = ['portrait', 'landscape'];
    }

    /**
     * 获取支持的格式
     * @returns {Array<string>} 支持的格式列表
     */
    getSupportedFormats() {
        return ['print', 'html'];
    }
    
    /**
     * 获取渲染器特性
     * @returns {Object} 特性对象
     */
    getFeatures() {
        return {
            supportsPagination: true,
            supportsPageBreaks: true,
            supportsPrintOptimization: true,
            supportsCustomPageSize: true,
            supportsMarginControl: true,
            supportsHeaderFooter: true,
            supportsWatermark: true,
            maxPageSize: 'A0',
            recommendedDPI: 300
        };
    }
    
    /**
     * 执行渲染 - 统一渲染架构方法
     * @param {DocumentModel} documentModel - 文档模型
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 渲染结果
     * @protected
     */
    async _executeRender(documentModel, options = {}) {
        try {
            // 验证文档模型
            if (!(documentModel instanceof DocumentModel)) {
                throw new Error('打印渲染器需要DocumentModel实例');
            }
            
            // 处理样式（使用父类的统一方法）
            const styles = await super._processStyles(documentModel, options);
            
            // 处理布局
            const layout = await super._processLayout(documentModel, options);
            
            // 处理位置
            const positions = await super._processPositions(documentModel, layout, options);
            
            // 生成打印优化的HTML内容
            const htmlContent = await this._generatePrintHTML(documentModel, {
                styles,
                layout,
                positions,
                ...options
            });
            
            // 添加打印功能
            const printReadyHTML = await this._addPrintFeatures(htmlContent, options);
            
            return {
                content: printReadyHTML,
                format: 'print',
                metadata: {
                    pageSize: layout.pageSize,
                    orientation: layout.orientation,
                    margins: layout.margins,
                    printOptimized: true
                }
            };
            
        } catch (error) {
            console.error('[PrintRenderer] 渲染失败:', error);
            throw error;
        }
    }
    
    /**
     * 执行实际渲染 - 重写父类方法，添加打印优化（保持向后兼容）
     * @param {Object} template - 模板对象
     * @param {Object} data - 预处理后的数据对象
     * @param {Object} options - 渲染选项配置
     * @returns {Promise<string>} 打印优化的HTML字符串
     * @protected
     */
    async _doRender(template, data, options) {
        // 添加打印专用CSS
        const printOptions = {
            ...options,
            customCSS: `${options.customCSS || ''}\n${this.printCSS}`
        };
        
        // 调用父类渲染方法
        const htmlResult = await super._doRender(template, data, printOptions);
        
        // 添加打印功能脚本
        const printEnhancedHTML = this._addPrintFeatures(htmlResult, printOptions);
        
        return printEnhancedHTML;
    }

    /**
     * 获取打印优化CSS - 返回专门为打印优化的CSS样式
     * @returns {string} 打印优化CSS
     * @private
     */
    _getPrintOptimizedCSS() {
        return `
        /* 打印优化样式 */
        @media print {
            /* 页面设置 */
            @page {
                margin: 20mm;
                size: A4;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            /* 基础重置 */
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
            
            body {
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                font-size: 12pt !important;
                line-height: 1.4 !important;
                color: #000 !important;
            }
            
            /* 容器优化 */
            .document-container {
                margin: 0 !important;
                padding: 0 !important;
                box-shadow: none !important;
                border: none !important;
                max-width: none !important;
                width: 100% !important;
                background: white !important;
            }
            
            /* 隐藏不必要的元素 */
            .no-print,
            .print-hidden,
            button,
            input[type="button"],
            input[type="submit"],
            .btn,
            nav,
            .navigation,
            .sidebar,
            .header-nav,
            .footer-nav {
                display: none !important;
            }
            
            /* 页面分割控制 */
            .page-break-before {
                page-break-before: always !important;
                break-before: page !important;
            }
            
            .page-break-after {
                page-break-after: always !important;
                break-after: page !important;
            }
            
            .page-break-inside-avoid {
                page-break-inside: avoid !important;
                break-inside: avoid !important;
            }
            
            /* 表格优化 */
            table {
                page-break-inside: auto !important;
                border-collapse: collapse !important;
            }
            
            tr {
                page-break-inside: avoid !important;
                page-break-after: auto !important;
            }
            
            th, td {
                page-break-inside: avoid !important;
                padding: 4pt 6pt !important;
                border: 1pt solid #000 !important;
            }
            
            thead {
                display: table-header-group !important;
            }
            
            tfoot {
                display: table-footer-group !important;
            }
            
            /* 标题优化 */
            h1, h2, h3, h4, h5, h6 {
                page-break-after: avoid !important;
                page-break-inside: avoid !important;
                margin-top: 12pt !important;
                margin-bottom: 6pt !important;
                font-weight: bold !important;
                color: #000 !important;
            }
            
            h1 { font-size: 18pt !important; }
            h2 { font-size: 16pt !important; }
            h3 { font-size: 14pt !important; }
            h4 { font-size: 12pt !important; }
            
            /* 段落优化 */
            p {
                orphans: 3 !important;
                widows: 3 !important;
                margin: 6pt 0 !important;
            }
            
            /* 列表优化 */
            ul, ol {
                page-break-inside: avoid !important;
                margin: 6pt 0 !important;
                padding-left: 20pt !important;
            }
            
            li {
                page-break-inside: avoid !important;
                margin: 3pt 0 !important;
            }
            
            /* 图片优化 */
            img {
                max-width: 100% !important;
                height: auto !important;
                page-break-inside: avoid !important;
                display: block !important;
                margin: 6pt auto !important;
            }
            
            /* 链接优化 */
            a {
                color: #000 !important;
                text-decoration: underline !important;
            }
            
            a[href]:after {
                content: " (" attr(href) ")";
                font-size: 10pt;
                color: #666;
            }
            
            /* 签名区域优化 */
            .signature-section {
                page-break-inside: avoid !important;
                margin-top: 30pt !important;
            }
            
            .signature-line {
                border-bottom: 1pt solid #000 !important;
                height: 30pt !important;
                margin: 10pt 0 !important;
            }
            
            .signature-box {
                page-break-inside: avoid !important;
            }
            
            /* 页脚信息 */
            .print-footer {
                position: fixed;
                bottom: 10mm;
                left: 0;
                right: 0;
                text-align: center;
                font-size: 8pt;
                color: #666;
            }
            
            /* 页码 */
            .page-number:after {
                content: counter(page);
            }
            
            /* 背景移除 */
            .remove-background * {
                background: transparent !important;
                background-color: transparent !important;
                background-image: none !important;
            }
        }
        
        /* 屏幕预览样式 */
        @media screen {
            .print-preview {
                border: 1px solid #ddd;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                margin: 20px auto;
                background: white;
            }
            
            .print-controls {
                text-align: center;
                margin: 20px 0;
                padding: 15px;
                background: #f5f5f5;
                border-radius: 5px;
            }
            
            .print-button {
                background: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 3px;
                cursor: pointer;
                margin: 0 10px;
                font-size: 14px;
            }
            
            .print-button:hover {
                background: #0056b3;
            }
            
            .print-settings {
                margin: 20px 0;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 5px;
            }
            
            .setting-group {
                margin: 10px 0;
            }
            
            .setting-label {
                display: inline-block;
                width: 120px;
                font-weight: bold;
            }
            
            .setting-control {
                margin-left: 10px;
            }
        }
        `;
    }

    /**
     * 添加打印功能 - 为HTML添加打印控制功能
     * @param {string} htmlContent - 原始HTML内容
     * @param {Object} options - 渲染选项
     * @returns {string} 增强的HTML内容
     * @private
     */
    _addPrintFeatures(htmlContent, options) {
        // 如果不需要打印控制，直接返回
        if (!options.printPreview && !options.showPrintDialog) {
            return htmlContent;
        }
        
        const printControls = this._generatePrintControls(options);
        const printScript = this._generatePrintScript(options);
        
        // 在body结束标签前插入打印控制
        const bodyEndIndex = htmlContent.lastIndexOf('</body>');
        if (bodyEndIndex !== -1) {
            return htmlContent.slice(0, bodyEndIndex) + 
                   printControls + 
                   printScript + 
                   htmlContent.slice(bodyEndIndex);
        }
        
        return htmlContent + printControls + printScript;
    }

    /**
     * 生成打印控制界面 - 创建打印按钮和设置界面
     * @param {Object} options - 渲染选项
     * @returns {string} 打印控制HTML
     * @private
     */
    _generatePrintControls(options) {
        if (!options.printPreview) {
            return '';
        }
        
        return `
        <div class="print-controls no-print">
            <button class="print-button" onclick="printDocument()">
                🖨️ 打印文档
            </button>
            <button class="print-button" onclick="printPreview()">
                👁️ 打印预览
            </button>
            <button class="print-button" onclick="togglePrintSettings()">
                ⚙️ 打印设置
            </button>
        </div>
        
        <div id="printSettings" class="print-settings no-print" style="display: none;">
            <div class="setting-group">
                <span class="setting-label">页面大小:</span>
                <select id="pageSize" class="setting-control">
                    <option value="A4" ${options.pageSize === 'A4' ? 'selected' : ''}>A4</option>
                    <option value="A3" ${options.pageSize === 'A3' ? 'selected' : ''}>A3</option>
                    <option value="Letter" ${options.pageSize === 'Letter' ? 'selected' : ''}>Letter</option>
                </select>
            </div>
            
            <div class="setting-group">
                <span class="setting-label">页面方向:</span>
                <select id="orientation" class="setting-control">
                    <option value="portrait" ${options.orientation === 'portrait' ? 'selected' : ''}>纵向</option>
                    <option value="landscape" ${options.orientation === 'landscape' ? 'selected' : ''}>横向</option>
                </select>
            </div>
            
            <div class="setting-group">
                <span class="setting-label">边距:</span>
                <input type="text" id="margins" class="setting-control" 
                       value="${options.margins || '20mm'}" placeholder="20mm">
            </div>
            
            <div class="setting-group">
                <span class="setting-label">移除背景:</span>
                <input type="checkbox" id="removeBackground" class="setting-control" 
                       ${options.removeBackground ? 'checked' : ''}>
            </div>
            
            <div class="setting-group">
                <button class="print-button" onclick="applyPrintSettings()">应用设置</button>
            </div>
        </div>
        `;
    }

    /**
     * 生成打印脚本 - 创建打印控制JavaScript代码
     * @param {Object} options - 渲染选项
     * @returns {string} 打印脚本HTML
     * @private
     */
    _generatePrintScript(options) {
        return `
        <script type="text/javascript">
            // 打印文档
            function printDocument() {
                window.print();
            }
            
            // 打印预览
            function printPreview() {
                const printWindow = window.open('', '_blank');
                const documentClone = document.documentElement.cloneNode(true);
                
                // 移除打印控制元素
                const noPrintElements = documentClone.querySelectorAll('.no-print');
                noPrintElements.forEach(el => el.remove());
                
                printWindow.document.write('<!DOCTYPE html>');
                printWindow.document.write(documentClone.outerHTML);
                printWindow.document.close();
                
                printWindow.focus();
                printWindow.print();
                printWindow.close();
            }
            
            // 切换打印设置显示
            function togglePrintSettings() {
                const settings = document.getElementById('printSettings');
                if (settings) {
                    settings.style.display = settings.style.display === 'none' ? 'block' : 'none';
                }
            }
            
            // 应用打印设置
            function applyPrintSettings() {
                const pageSize = document.getElementById('pageSize')?.value || 'A4';
                const orientation = document.getElementById('orientation')?.value || 'portrait';
                const margins = document.getElementById('margins')?.value || '20mm';
                const removeBackground = document.getElementById('removeBackground')?.checked || false;
                
                // 更新页面样式
                updatePageStyle(pageSize, orientation, margins, removeBackground);
                
                alert('打印设置已应用');
            }
            
            // 更新页面样式
            function updatePageStyle(pageSize, orientation, margins, removeBackground) {
                // 移除现有的打印样式
                const existingStyle = document.getElementById('dynamicPrintStyle');
                if (existingStyle) {
                    existingStyle.remove();
                }
                
                // 创建新的打印样式
                const style = document.createElement('style');
                style.id = 'dynamicPrintStyle';
                style.innerHTML = \`
                    @media print {
                        @page {
                            size: \${pageSize} \${orientation};
                            margin: \${margins};
                        }
                        \${removeBackground ? '.remove-background * { background: transparent !important; }' : ''}
                    }
                \`;
                
                document.head.appendChild(style);
            }
            
            // 自动打印（如果启用）
            ${options.autoprint ? `
            window.addEventListener('load', function() {
                setTimeout(function() {
                    window.print();
                }, 1000);
            });
            ` : ''}
            
            // 键盘快捷键
            document.addEventListener('keydown', function(e) {
                if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                    e.preventDefault();
                    printDocument();
                }
            });
            
            // 打印前事件
            window.addEventListener('beforeprint', function() {
                console.log('准备打印文档');
                // 可以在这里添加打印前的处理逻辑
            });
            
            // 打印后事件
            window.addEventListener('afterprint', function() {
                console.log('打印完成');
                // 可以在这里添加打印后的处理逻辑
            });
        </script>
        `;
    }

    /**
     * 优化打印布局 - 根据内容自动调整页面分割
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 渲染选项
     * @returns {string} 优化后的HTML内容
     */
    optimizePrintLayout(htmlContent, options = {}) {
        if (!options.enablePageBreaks) {
            return htmlContent;
        }
        
        // 创建DOM解析器
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        
        // 为长表格添加分页控制
        const tables = doc.querySelectorAll('table');
        tables.forEach(table => {
            if (table.rows.length > 20) { // 如果表格行数超过20
                this._addTablePageBreaks(table);
            }
        });
        
        // 为长段落添加分页控制
        const paragraphs = doc.querySelectorAll('p');
        paragraphs.forEach(p => {
            if (p.textContent.length > 1000) { // 如果段落过长
                p.classList.add('page-break-inside-avoid');
            }
        });
        
        // 为签名区域添加分页保护
        const signatures = doc.querySelectorAll('.signature-section');
        signatures.forEach(sig => {
            sig.classList.add('page-break-inside-avoid');
        });
        
        return doc.documentElement.outerHTML;
    }

    /**
     * 为表格添加分页控制 - 在适当位置插入分页符
     * @param {Element} table - 表格元素
     * @private
     */
    _addTablePageBreaks(table) {
        const rows = Array.from(table.rows);
        const pageSize = 25; // 每页最多25行
        
        for (let i = pageSize; i < rows.length; i += pageSize) {
            if (rows[i]) {
                rows[i].classList.add('page-break-before');
            }
        }
    }

    /**
     * 创建打印样式表 - 生成独立的打印CSS文件内容
     * @param {Object} options - 渲染选项
     * @returns {string} 打印CSS内容
     */
    createPrintStylesheet(options = {}) {
        const baseCSS = this._getPrintOptimizedCSS();
        const customCSS = options.customCSS || '';
        
        return `${baseCSS}\n\n/* 自定义打印样式 */\n${customCSS}`;
    }

    /**
     * 估算打印页数 - 根据内容估算打印页数
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 渲染选项
     * @returns {Object} 页数估算信息
     */
    estimatePrintPages(htmlContent, options = {}) {
        // 简单的页数估算逻辑
        const pageHeight = options.pageSize === 'A4' ? 297 : 
                          options.pageSize === 'A3' ? 420 : 297; // mm
        
        const contentHeight = this._estimateContentHeight(htmlContent);
        const estimatedPages = Math.ceil(contentHeight / pageHeight);
        
        return {
            estimatedPages,
            pageSize: options.pageSize || 'A4',
            orientation: options.orientation || 'portrait',
            contentHeight: `${contentHeight}mm`
        };
    }

    /**
     * 估算内容高度 - 根据HTML内容估算总高度
     * @param {string} htmlContent - HTML内容
     * @returns {number} 估算高度（mm）
     * @private
     */
    _estimateContentHeight(htmlContent) {
        // 简化的高度估算
        const lineHeight = 5; // mm per line
        const lines = htmlContent.split('\n').length;
        const tableCount = (htmlContent.match(/<table/g) || []).length;
        const imageCount = (htmlContent.match(/<img/g) || []).length;
        
        const baseHeight = lines * lineHeight;
        const tableHeight = tableCount * 50; // 每个表格约50mm
        const imageHeight = imageCount * 30; // 每个图片约30mm
        
        return baseHeight + tableHeight + imageHeight;
    }

    /**
     * 获取打印设置 - 返回当前的打印配置
     * @returns {Object} 打印设置对象
     */
    getPrintSettings() {
        return {
            pageSize: this.renderConfig.pageSize,
            orientation: this.renderConfig.orientation,
            margins: this.renderConfig.margins,
            removeBackground: this.renderConfig.removeBackground,
            enablePageBreaks: this.renderConfig.enablePageBreaks,
            printOptimized: this.renderConfig.printOptimized
        };
    }

    /**
     * 设置打印配置 - 更新打印渲染器的配置
     * @param {Object} settings - 打印设置
     */
    setPrintSettings(settings) {
        this.renderConfig = {
            ...this.renderConfig,
            ...settings
        };
    }

    /**
     * 验证打印设置 - 检查打印设置的有效性
     * @param {Object} settings - 打印设置
     * @returns {Object} 验证结果
     */
    validatePrintSettings(settings) {
        const errors = [];
        const warnings = [];
        
        // 检查页面大小
        if (settings.pageSize && !this.supportedPageSizes.includes(settings.pageSize)) {
            errors.push(`不支持的页面大小: ${settings.pageSize}`);
        }
        
        // 检查页面方向
        if (settings.orientation && !this.supportedOrientations.includes(settings.orientation)) {
            errors.push(`不支持的页面方向: ${settings.orientation}`);
        }
        
        // 检查边距格式
        if (settings.margins && !/^\d+(mm|cm|in|pt)$/.test(settings.margins)) {
            warnings.push('边距格式可能不正确，建议使用如"20mm"的格式');
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建打印渲染器实例 - 工厂函数，方便创建打印渲染器
 * @param {Object} config - 渲染器配置
 * @returns {PrintRenderer} 打印渲染器实例
 */
export function createPrintRenderer(config = {}) {
    return new PrintRenderer(config);
}

/**
 * 创建预设打印渲染器 - 创建具有预定义配置的打印渲染器
 * @param {string} preset - 预设名称 ('default', 'minimal', 'high-quality', 'draft')
 * @param {Object} config - 额外配置
 * @returns {PrintRenderer} 打印渲染器实例
 */
export function createPresetPrintRenderer(preset = 'default', config = {}) {
    const presetConfigs = {
        default: {
            renderConfig: {
                pageSize: 'A4',
                orientation: 'portrait',
                margins: '20mm',
                removeBackground: false,
                enablePageBreaks: true,
                printPreview: true
            }
        },
        
        minimal: {
            renderConfig: {
                pageSize: 'A4',
                orientation: 'portrait',
                margins: '15mm',
                removeBackground: true,
                enablePageBreaks: true,
                printPreview: false,
                showPrintDialog: false
            }
        },
        
        'high-quality': {
            renderConfig: {
                pageSize: 'A4',
                orientation: 'portrait',
                margins: '25mm',
                removeBackground: false,
                enablePageBreaks: true,
                printPreview: true,
                scaleToFit: false
            }
        },
        
        draft: {
            renderConfig: {
                pageSize: 'A4',
                orientation: 'portrait',
                margins: '10mm',
                removeBackground: true,
                enablePageBreaks: false,
                printPreview: false
            }
        }
    };
    
    const presetConfig = presetConfigs[preset] || presetConfigs.default;
    const mergedConfig = {
        ...presetConfig,
        ...config,
        renderConfig: {
            ...presetConfig.renderConfig,
            ...config.renderConfig
        }
    };
    
    return new PrintRenderer(mergedConfig);
}
// #endregion

    // 注意：样式、布局、位置处理方法已移至父类UnifiedRenderer
    // 打印特定的处理逻辑通过共享服务的格式参数自动处理
    
    /**
     * 获取打印专用样式增强
     * @param {Object} baseStyles - 基础样式
     * @param {Object} options - 选项
     * @returns {Object} 增强后的样式
     * @private
     */
    _enhanceStylesForPrint(baseStyles, options) {
        const printStyles = this._getPrintSpecificStyles(options);
        return {
            ...baseStyles,
            print: printStyles,
            optimized: true
        };
    }
    
    /**
     * 获取打印专用布局增强
     * @param {Object} baseLayout - 基础布局
     * @param {Object} options - 选项
     * @returns {Object} 增强后的布局
     * @private
     */
    _enhanceLayoutForPrint(baseLayout, options) {
        const printLayout = this._calculatePrintLayout(options);
        return {
            ...baseLayout,
            ...printLayout,
            printOptimized: true
        };
    }
    
    /**
     * 获取打印专用位置增强
     * @param {Object} basePositions - 基础位置
     * @param {Object} options - 选项
     * @param {DocumentModel} documentModel - 文档模型（用于计算分页符）
     * @returns {Object} 增强后的位置
     * @private
     */
    _enhancePositionsForPrint(basePositions, options, documentModel = null) {
        const printPositions = this._getDefaultPrintPositions();
        return {
            ...basePositions,
            ...printPositions,
            pageBreaks: documentModel ? this._calculatePageBreaks(documentModel, options) : []
        };
    }
    
    /**
     * 生成打印优化的HTML内容
     * @param {DocumentModel} documentModel - 文档模型
     * @param {Object} renderData - 渲染数据
     * @returns {Promise<string>} HTML内容
     * @protected
     */
    async _generatePrintHTML(documentModel, renderData) {
        // 使用HTML渲染器生成基础内容
        const baseHTML = await this.htmlRenderer._executeRender(documentModel, {
            ...renderData,
            printMode: true
        });
        
        // 添加打印特定的结构
        return this._wrapWithPrintStructure(baseHTML.content, renderData);
    }
    
    /**
     * 添加打印功能
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 选项
     * @returns {Promise<string>} 增强的HTML内容
     * @protected
     */
    async _addPrintFeatures(htmlContent, options = {}) {
        let enhancedHTML = htmlContent;
        
        // 添加页眉页脚
        if (options.includeHeaderFooter !== false) {
            enhancedHTML = this._addHeaderFooter(enhancedHTML, options);
        }
        
        // 添加水印
        if (options.watermark) {
            enhancedHTML = this._addWatermark(enhancedHTML, options.watermark);
        }
        
        // 添加打印控制脚本
        enhancedHTML = this._addPrintControls(enhancedHTML, options);
        
        return enhancedHTML;
    }
    
    /**
     * 获取打印特定样式
     * @param {Object} options - 选项
     * @returns {Object} 打印样式
     * @protected
     */
    _getPrintSpecificStyles(options = {}) {
        const pageSize = options.pageSize || 'A4';
        const orientation = options.orientation || 'portrait';
        const margins = options.margins || this._getDefaultMargins();
        
        return {
            pageSize,
            orientation,
            margins,
            css: this._generatePrintCSS(pageSize, orientation, margins)
        };
    }
    
    /**
     * 计算打印布局
     * @param {Object} options - 选项
     * @returns {Object} 打印布局
     * @protected
     */
    _calculatePrintLayout(options = {}) {
        const pageSize = options.pageSize || 'A4';
        const orientation = options.orientation || 'portrait';
        const dimensions = this._getPageDimensions(pageSize, orientation);
        
        return {
            pageSize,
            orientation,
            dimensions,
            margins: options.margins || this._getDefaultMargins(),
            printableArea: this._calculatePrintableArea(dimensions, options.margins)
        };
    }
    
    /**
     * 获取默认打印位置
     * @returns {Object} 默认位置
     * @protected
     */
    _getDefaultPrintPositions() {
        return {
            header: { top: '10mm', left: '0', right: '0' },
            footer: { bottom: '10mm', left: '0', right: '0' },
            content: { top: '20mm', bottom: '20mm', left: '15mm', right: '15mm' }
        };
    }
    
    /**
     * 计算分页符
     * @param {DocumentModel} documentModel - 文档模型
     * @param {Object} options - 选项
     * @returns {Array} 分页符位置
     * @protected
     */
    _calculatePageBreaks(documentModel, options = {}) {
        const pageBreaks = [];
        const content = documentModel.getContent();
        
        // 根据内容长度和页面高度计算分页
        if (options.autoPageBreak !== false) {
            const pageHeight = this._getPageHeight(options.pageSize, options.orientation);
            const contentHeight = this._estimateContentHeight(content);
            const pagesNeeded = Math.ceil(contentHeight / pageHeight);
            
            for (let i = 1; i < pagesNeeded; i++) {
                pageBreaks.push({
                    position: i * pageHeight,
                    type: 'auto'
                });
            }
        }
        
        return pageBreaks;
    }
    
    /**
     * 包装打印结构
     * @param {string} content - 内容
     * @param {Object} renderData - 渲染数据
     * @returns {string} 包装后的HTML
     * @protected
     */
    _wrapWithPrintStructure(content, renderData) {
        const { styles, layout } = renderData;
        
        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打印文档</title>
    <style>
        ${this._generatePrintCSS(layout.pageSize, layout.orientation, layout.margins)}
        ${styles.print.css}
    </style>
</head>
<body class="print-document">
    <div class="print-container">
        ${content}
    </div>
</body>
</html>`;
    }
    
    /**
     * 添加页眉页脚
     * @param {string} html - HTML内容
     * @param {Object} options - 选项
     * @returns {string} 增强的HTML
     * @protected
     */
    _addHeaderFooter(html, options) {
        const header = options.header || this._getDefaultHeader();
        const footer = options.footer || this._getDefaultFooter();
        
        return html.replace(
            '<div class="print-container">',
            `<div class="print-container">
                <div class="print-header">${header}</div>
                <div class="print-content">`
        ).replace(
            '</div>\n</body>',
            `    </div>
                <div class="print-footer">${footer}</div>
            </div>
        </body>`
        );
    }
    
    /**
     * 添加水印
     * @param {string} html - HTML内容
     * @param {string|Object} watermark - 水印配置
     * @returns {string} 增强的HTML
     * @protected
     */
    _addWatermark(html, watermark) {
        const watermarkHTML = typeof watermark === 'string' 
            ? `<div class="watermark">${watermark}</div>`
            : `<div class="watermark" style="${this._getWatermarkStyles(watermark)}">${watermark.text}</div>`;
        
        return html.replace(
            '<div class="print-container">',
            `<div class="print-container">
                ${watermarkHTML}`
        );
    }
    
    /**
     * 添加打印控制
     * @param {string} html - HTML内容
     * @param {Object} options - 选项
     * @returns {string} 增强的HTML
     * @protected
     */
    _addPrintControls(html, options) {
        const printScript = `
        <script>
            // 打印控制脚本
            window.addEventListener('load', function() {
                if (${options.autoPrint === true}) {
                    window.print();
                }
            });
            
            // 打印前处理
            window.addEventListener('beforeprint', function() {
                document.body.classList.add('printing');
            });
            
            // 打印后处理
            window.addEventListener('afterprint', function() {
                document.body.classList.remove('printing');
            });
        </script>`;
        
        return html.replace('</body>', `${printScript}\n</body>`);
    }
    
    /**
     * 生成打印CSS
     * @param {string} pageSize - 页面大小
     * @param {string} orientation - 方向
     * @param {Object} margins - 边距
     * @returns {string} CSS字符串
     * @protected
     */
    _generatePrintCSS(pageSize, orientation, margins) {
        return `
        @page {
            size: ${pageSize} ${orientation};
            margin: ${this._formatMargins(margins)};
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
                font-size: 12pt;
                line-height: 1.4;
                color: #000;
                background: #fff;
            }
            
            .print-container {
                width: 100%;
                height: 100%;
                position: relative;
            }
            
            .print-header, .print-footer {
                position: fixed;
                left: 0;
                right: 0;
                z-index: 1000;
            }
            
            .print-header {
                top: 0;
            }
            
            .print-footer {
                bottom: 0;
            }
            
            .watermark {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) rotate(-45deg);
                font-size: 48pt;
                color: rgba(0, 0, 0, 0.1);
                z-index: -1;
                pointer-events: none;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            .no-print {
                display: none !important;
            }
        }`;
    }
    
    /**
     * 获取页面尺寸
     * @param {string} pageSize - 页面大小
     * @param {string} orientation - 方向
     * @returns {Object} 尺寸对象
     * @protected
     */
    _getPageDimensions(pageSize, orientation) {
        const sizes = {
            'A4': { width: 210, height: 297 },
            'A3': { width: 297, height: 420 },
            'A5': { width: 148, height: 210 },
            'Letter': { width: 216, height: 279 },
            'Legal': { width: 216, height: 356 }
        };
        
        const size = sizes[pageSize] || sizes['A4'];
        
        return orientation === 'landscape' 
            ? { width: size.height, height: size.width }
            : size;
    }
    
    /**
     * 获取默认边距
     * @returns {Object} 边距对象
     * @protected
     */
    _getDefaultMargins() {
        return {
            top: '20mm',
            right: '15mm',
            bottom: '20mm',
            left: '15mm'
        };
    }
    
    /**
     * 格式化边距
     * @param {Object} margins - 边距对象
     * @returns {string} CSS边距字符串
     * @protected
     */
    _formatMargins(margins) {
        if (!margins) margins = this._getDefaultMargins();
        return `${margins.top} ${margins.right} ${margins.bottom} ${margins.left}`;
    }
    
    /**
     * 计算可打印区域
     * @param {Object} dimensions - 页面尺寸
     * @param {Object} margins - 边距
     * @returns {Object} 可打印区域
     * @protected
     */
    _calculatePrintableArea(dimensions, margins) {
        if (!margins) margins = this._getDefaultMargins();
        
        const marginTop = this._parseMargin(margins.top);
        const marginRight = this._parseMargin(margins.right);
        const marginBottom = this._parseMargin(margins.bottom);
        const marginLeft = this._parseMargin(margins.left);
        
        return {
            width: dimensions.width - marginLeft - marginRight,
            height: dimensions.height - marginTop - marginBottom
        };
    }
    
    /**
     * 解析边距值
     * @param {string} margin - 边距字符串
     * @returns {number} 边距数值(mm)
     * @protected
     */
    _parseMargin(margin) {
        if (typeof margin === 'number') return margin;
        if (typeof margin === 'string') {
            const value = parseFloat(margin);
            if (margin.includes('mm')) return value;
            if (margin.includes('cm')) return value * 10;
            if (margin.includes('in')) return value * 25.4;
            return value; // 假设是mm
        }
        return 15; // 默认15mm
    }
    
    /**
     * 获取页面高度
     * @param {string} pageSize - 页面大小
     * @param {string} orientation - 方向
     * @returns {number} 页面高度(mm)
     * @protected
     */
    _getPageHeight(pageSize, orientation) {
        const dimensions = this._getPageDimensions(pageSize, orientation);
        return dimensions.height;
    }
    
    /**
     * 估算内容高度
     * @param {string} content - 内容
     * @returns {number} 估算高度(mm)
     * @protected
     */
    _estimateContentHeight(content) {
        // 简单的高度估算，基于字符数和行数
        const lines = content.split('\n').length;
        const avgLineHeight = 5; // mm
        return lines * avgLineHeight;
    }
    
    /**
     * 获取默认页眉
     * @returns {string} 页眉HTML
     * @protected
     */
    _getDefaultHeader() {
        return `<div style="text-align: center; font-size: 10pt; padding: 5mm 0;">
            文档标题 - 第 <span class="page-number"></span> 页
        </div>`;
    }
    
    /**
     * 获取默认页脚
     * @returns {string} 页脚HTML
     * @protected
     */
    _getDefaultFooter() {
        const now = new Date();
        return `<div style="text-align: center; font-size: 9pt; padding: 5mm 0;">
            打印时间: ${DateUtils.formatDate(now, 'YYYY-MM-DD HH:mm:ss')}
        </div>`;
    }
    
    /**
     * 获取水印样式
     * @param {Object} watermark - 水印配置
     * @returns {string} CSS样式
     * @protected
     */
    _getWatermarkStyles(watermark) {
        return `
            opacity: ${watermark.opacity || 0.1};
            font-size: ${watermark.fontSize || '48pt'};
            color: ${watermark.color || '#000'};
            transform: translate(-50%, -50%) rotate(${watermark.rotation || -45}deg);
        `;
    }

    /**
     * 获取支持的格式
     * @returns {Array<string>} 支持的格式列表
     */
    getSupportedFormats() {
        return ['print', 'html'];
    }
    
    /**
     * 获取渲染器特性
     * @returns {Object} 特性对象
     */
    getFeatures() {
        return {
            supportsPagination: true,
            supportsPageBreaks: true,
            supportsPrintOptimization: true,
            supportsCustomPageSize: true,
            supportsMarginControl: true,
            supportsHeaderFooter: true,
            supportsWatermark: true,
            maxPageSize: 'A0',
            recommendedDPI: 300
        };
    }
    
    /**
     * 执行渲染 - 统一渲染架构方法
     * @param {DocumentModel} documentModel - 文档模型
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 渲染结果
     * @protected
     */
    async _executeRender(documentModel, options = {}) {
        try {
            // 验证文档模型
            if (!(documentModel instanceof DocumentModel)) {
                throw new Error('打印渲染器需要DocumentModel实例');
            }
            
            // 处理样式（使用父类的统一方法）
            const styles = await super._processStyles(documentModel, options);
            
            // 处理布局
            const layout = await super._processLayout(documentModel, options);
            
            // 处理位置
            const positions = await super._processPositions(documentModel, layout, options);
            
            // 生成打印优化的HTML内容
            const htmlContent = await this._generatePrintHTML(documentModel, {
                styles,
                layout,
                positions,
                ...options
            });
            
            // 添加打印功能
            const printReadyHTML = await this._addPrintFeatures(htmlContent, options);
            
            return {
                content: printReadyHTML,
                format: 'print',
                metadata: {
                    pageSize: layout.pageSize,
                    orientation: layout.orientation,
                    margins: layout.margins,
                    printOptimized: true
                }
            };
            
        } catch (error) {
            console.error('[PrintRenderer] 渲染失败:', error);
            throw error;
        }
    }
    
    /**
     * 执行实际渲染 - 重写父类方法，添加打印优化（保持向后兼容）
     * @param {Object} template - 模板对象
     * @param {Object} data - 预处理后的数据对象
     * @param {Object} options - 渲染选项配置
     * @returns {Promise<string>} 打印优化的HTML字符串
     * @protected
     */
    async _doRender(template, data, options) {
        // 添加打印专用CSS
        const printOptions = {
            ...options,
            customCSS: `${options.customCSS || ''}\n${this.printCSS}`
        };
        
        // 调用父类渲染方法
        const htmlResult = await super._doRender(template, data, printOptions);
        
        // 添加打印功能脚本
        const printEnhancedHTML = this._addPrintFeatures(htmlResult, printOptions);
        
        return printEnhancedHTML;
    }

    /**
     * 获取打印优化CSS - 返回专门为打印优化的CSS样式
     * @returns {string} 打印优化CSS
     * @private
     */
    _getPrintOptimizedCSS() {
        return `
        /* 打印优化样式 */
        @media print {
            /* 页面设置 */
            @page {
                margin: 20mm;
                size: A4;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            /* 基础重置 */
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
            
            body {
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                font-size: 12pt !important;
                line-height: 1.4 !important;
                color: #000 !important;
            }
            
            /* 容器优化 */
            .document-container {
                margin: 0 !important;
                padding: 0 !important;
                box-shadow: none !important;
                border: none !important;
                max-width: none !important;
                width: 100% !important;
                background: white !important;
            }
            
            /* 隐藏不必要的元素 */
            .no-print,
            .print-hidden,
            button,
            input[type="button"],
            input[type="submit"],
            .btn,
            nav,
            .navigation,
            .sidebar,
            .header-nav,
            .footer-nav {
                display: none !important;
            }
            
            /* 页面分割控制 */
            .page-break-before {
                page-break-before: always !important;
                break-before: page !important;
            }
            
            .page-break-after {
                page-break-after: always !important;
                break-after: page !important;
            }
            
            .page-break-inside-avoid {
                page-break-inside: avoid !important;
                break-inside: avoid !important;
            }
            
            /* 表格优化 */
            table {
                page-break-inside: auto !important;
                border-collapse: collapse !important;
            }
            
            tr {
                page-break-inside: avoid !important;
                page-break-after: auto !important;
            }
            
            th, td {
                page-break-inside: avoid !important;
                padding: 4pt 6pt !important;
                border: 1pt solid #000 !important;
            }
            
            thead {
                display: table-header-group !important;
            }
            
            tfoot {
                display: table-footer-group !important;
            }
            
            /* 标题优化 */
            h1, h2, h3, h4, h5, h6 {
                page-break-after: avoid !important;
                page-break-inside: avoid !important;
                margin-top: 12pt !important;
                margin-bottom: 6pt !important;
                font-weight: bold !important;
                color: #000 !important;
            }
            
            h1 { font-size: 18pt !important; }
            h2 { font-size: 16pt !important; }
            h3 { font-size: 14pt !important; }
            h4 { font-size: 12pt !important; }
            
            /* 段落优化 */
            p {
                orphans: 3 !important;
                widows: 3 !important;
                margin: 6pt 0 !important;
            }
            
            /* 列表优化 */
            ul, ol {
                page-break-inside: avoid !important;
                margin: 6pt 0 !important;
                padding-left: 20pt !important;
            }
            
            li {
                page-break-inside: avoid !important;
                margin: 3pt 0 !important;
            }
            
            /* 图片优化 */
            img {
                max-width: 100% !important;
                height: auto !important;
                page-break-inside: avoid !important;
                display: block !important;
                margin: 6pt auto !important;
            }
            
            /* 链接优化 */
            a {
                color: #000 !important;
                text-decoration: underline !important;
            }
            
            a[href]:after {
                content: " (" attr(href) ")";
                font-size: 10pt;
                color: #666;
            }
            
            /* 签名区域优化 */
            .signature-section {
                page-break-inside: avoid !important;
                margin-top: 30pt !important;
            }
            
            .signature-line {
                border-bottom: 1pt solid #000 !important;
                height: 30pt !important;
                margin: 10pt 0 !important;
            }
            
            .signature-box {
                page-break-inside: avoid !important;
            }
            
            /* 页脚信息 */
            .print-footer {
                position: fixed;
                bottom: 10mm;
                left: 0;
                right: 0;
                text-align: center;
                font-size: 8pt;
                color: #666;
            }
            
            /* 页码 */
            .page-number:after {
                content: counter(page);
            }
            
            /* 背景移除 */
            .remove-background * {
                background: transparent !important;
                background-color: transparent !important;
                background-image: none !important;
            }
        }
        
        /* 屏幕预览样式 */
        @media screen {
            .print-preview {
                border: 1px solid #ddd;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                margin: 20px auto;
                background: white;
            }
            
            .print-controls {
                text-align: center;
                margin: 20px 0;
                padding: 15px;
                background: #f5f5f5;
                border-radius: 5px;
            }
            
            .print-button {
                background: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 3px;
                cursor: pointer;
                margin: 0 10px;
                font-size: 14px;
            }
            
            .print-button:hover {
                background: #0056b3;
            }
            
            .print-settings {
                margin: 20px 0;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 5px;
            }
            
            .setting-group {
                margin: 10px 0;
            }
            
            .setting-label {
                display: inline-block;
                width: 120px;
                font-weight: bold;
            }
            
            .setting-control {
                margin-left: 10px;
            }
        }
        `;
    }

    /**
     * 添加打印功能 - 为HTML添加打印控制功能
     * @param {string} htmlContent - 原始HTML内容
     * @param {Object} options - 渲染选项
     * @returns {string} 增强的HTML内容
     * @private
     */
    _addPrintFeatures(htmlContent, options) {
        // 如果不需要打印控制，直接返回
        if (!options.printPreview && !options.showPrintDialog) {
            return htmlContent;
        }
        
        const printControls = this._generatePrintControls(options);
        const printScript = this._generatePrintScript(options);
        
        // 在body结束标签前插入打印控制
        const bodyEndIndex = htmlContent.lastIndexOf('</body>');
        if (bodyEndIndex !== -1) {
            return htmlContent.slice(0, bodyEndIndex) + 
                   printControls + 
                   printScript + 
                   htmlContent.slice(bodyEndIndex);
        }
        
        return htmlContent + printControls + printScript;
    }

    /**
     * 生成打印控制界面 - 创建打印按钮和设置界面
     * @param {Object} options - 渲染选项
     * @returns {string} 打印控制HTML
     * @private
     */
    _generatePrintControls(options) {
        if (!options.printPreview) {
            return '';
        }
        
        return `
        <div class="print-controls no-print">
            <button class="print-button" onclick="printDocument()">
                🖨️ 打印文档
            </button>
            <button class="print-button" onclick="printPreview()">
                👁️ 打印预览
            </button>
            <button class="print-button" onclick="togglePrintSettings()">
                ⚙️ 打印设置
            </button>
        </div>
        
        <div id="printSettings" class="print-settings no-print" style="display: none;">
            <div class="setting-group">
                <span class="setting-label">页面大小:</span>
                <select id="pageSize" class="setting-control">
                    <option value="A4" ${options.pageSize === 'A4' ? 'selected' : ''}>A4</option>
                    <option value="A3" ${options.pageSize === 'A3' ? 'selected' : ''}>A3</option>
                    <option value="Letter" ${options.pageSize === 'Letter' ? 'selected' : ''}>Letter</option>
                </select>
            </div>
            
            <div class="setting-group">
                <span class="setting-label">页面方向:</span>
                <select id="orientation" class="setting-control">
                    <option value="portrait" ${options.orientation === 'portrait' ? 'selected' : ''}>纵向</option>
                    <option value="landscape" ${options.orientation === 'landscape' ? 'selected' : ''}>横向</option>
                </select>
            </div>
            
            <div class="setting-group">
                <span class="setting-label">边距:</span>
                <input type="text" id="margins" class="setting-control" 
                       value="${options.margins || '20mm'}" placeholder="20mm">
            </div>
            
            <div class="setting-group">
                <span class="setting-label">移除背景:</span>
                <input type="checkbox" id="removeBackground" class="setting-control" 
                       ${options.removeBackground ? 'checked' : ''}>
            </div>
            
            <div class="setting-group">
                <button class="print-button" onclick="applyPrintSettings()">应用设置</button>
            </div>
        </div>
        `;
    }

    /**
     * 生成打印脚本 - 创建打印控制JavaScript代码
     * @param {Object} options - 渲染选项
     * @returns {string} 打印脚本HTML
     * @private
     */
    _generatePrintScript(options) {
        return `
        <script type="text/javascript">
            // 打印文档
            function printDocument() {
                window.print();
            }
            
            // 打印预览
            function printPreview() {
                const printWindow = window.open('', '_blank');
                const documentClone = document.documentElement.cloneNode(true);
                
                // 移除打印控制元素
                const noPrintElements = documentClone.querySelectorAll('.no-print');
                noPrintElements.forEach(el => el.remove());
                
                printWindow.document.write('<!DOCTYPE html>');
                printWindow.document.write(documentClone.outerHTML);
                printWindow.document.close();
                
                printWindow.focus();
                printWindow.print();
                printWindow.close();
            }
            
            // 切换打印设置显示
            function togglePrintSettings() {
                const settings = document.getElementById('printSettings');
                if (settings) {
                    settings.style.display = settings.style.display === 'none' ? 'block' : 'none';
                }
            }
            
            // 应用打印设置
            function applyPrintSettings() {
                const pageSize = document.getElementById('pageSize')?.value || 'A4';
                const orientation = document.getElementById('orientation')?.value || 'portrait';
                const margins = document.getElementById('margins')?.value || '20mm';
                const removeBackground = document.getElementById('removeBackground')?.checked || false;
                
                // 更新页面样式
                updatePageStyle(pageSize, orientation, margins, removeBackground);
                
                alert('打印设置已应用');
            }
            
            // 更新页面样式
            function updatePageStyle(pageSize, orientation, margins, removeBackground) {
                // 移除现有的打印样式
                const existingStyle = document.getElementById('dynamicPrintStyle');
                if (existingStyle) {
                    existingStyle.remove();
                }
                
                // 创建新的打印样式
                const style = document.createElement('style');
                style.id = 'dynamicPrintStyle';
                style.innerHTML = \`
                    @media print {
                        @page {
                            size: \${pageSize} \${orientation};
                            margin: \${margins};
                        }
                        \${removeBackground ? '.remove-background * { background: transparent !important; }' : ''}
                    }
                \`;
                
                document.head.appendChild(style);
            }
            
            // 自动打印（如果启用）
            ${options.autoprint ? `
            window.addEventListener('load', function() {
                setTimeout(function() {
                    window.print();
                }, 1000);
            });
            ` : ''}
            
            // 键盘快捷键
            document.addEventListener('keydown', function(e) {
                if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                    e.preventDefault();
                    printDocument();
                }
            });
            
            // 打印前事件
            window.addEventListener('beforeprint', function() {
                console.log('准备打印文档');
                // 可以在这里添加打印前的处理逻辑
            });
            
            // 打印后事件
            window.addEventListener('afterprint', function() {
                console.log('打印完成');
                // 可以在这里添加打印后的处理逻辑
            });
        </script>
        `;
    }

    /**
     * 优化打印布局 - 根据内容自动调整页面分割
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 渲染选项
     * @returns {string} 优化后的HTML内容
     */
    optimizePrintLayout(htmlContent, options = {}) {
        if (!options.enablePageBreaks) {
            return htmlContent;
        }
        
        // 创建DOM解析器
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        
        // 为长表格添加分页控制
        const tables = doc.querySelectorAll('table');
        tables.forEach(table => {
            if (table.rows.length > 20) { // 如果表格行数超过20
                this._addTablePageBreaks(table);
            }
        });
        
        // 为长段落添加分页控制
        const paragraphs = doc.querySelectorAll('p');
        paragraphs.forEach(p => {
            if (p.textContent.length > 1000) { // 如果段落过长
                p.classList.add('page-break-inside-avoid');
            }
        });
        
        // 为签名区域添加分页保护
        const signatures = doc.querySelectorAll('.signature-section');
        signatures.forEach(sig => {
            sig.classList.add('page-break-inside-avoid');
        });
        
        return doc.documentElement.outerHTML;
    }

    /**
     * 为表格添加分页控制 - 在适当位置插入分页符
     * @param {Element} table - 表格元素
     * @private
     */
    _addTablePageBreaks(table) {
        const rows = Array.from(table.rows);
        const pageSize = 25; // 每页最多25行
        
        for (let i = pageSize; i < rows.length; i += pageSize) {
            if (rows[i]) {
                rows[i].classList.add('page-break-before');
            }
        }
    }

    /**
     * 创建打印样式表 - 生成独立的打印CSS文件内容
     * @param {Object} options - 渲染选项
     * @returns {string} 打印CSS内容
     */
    createPrintStylesheet(options = {}) {
        const baseCSS = this._getPrintOptimizedCSS();
        const customCSS = options.customCSS || '';
        
        return `${baseCSS}\n\n/* 自定义打印样式 */\n${customCSS}`;
    }

    /**
     * 估算打印页数 - 根据内容估算打印页数
     * @param {string} htmlContent - HTML内容
     * @param {Object} options - 渲染选项
     * @returns {Object} 页数估算信息
     */
    estimatePrintPages(htmlContent, options = {}) {
        // 简单的页数估算逻辑
        const pageHeight = options.pageSize === 'A4' ? 297 : 
                          options.pageSize === 'A3' ? 420 : 297; // mm
        
        const contentHeight = this._estimateContentHeight(htmlContent);
        const estimatedPages = Math.ceil(contentHeight / pageHeight);
        
        return {
            estimatedPages,
            pageSize: options.pageSize || 'A4',
            orientation: options.orientation || 'portrait',
            contentHeight: `${contentHeight}mm`
        };
    }

    /**
     * 估算内容高度 - 根据HTML内容估算总高度
     * @param {string} htmlContent - HTML内容
     * @returns {number} 估算高度（mm）
     * @private
     */
    _estimateContentHeight(htmlContent) {
        // 简化的高度估算
        const lineHeight = 5; // mm per line
        const lines = htmlContent.split('\n').length;
        const tableCount = (htmlContent.match(/<table/g) || []).length;
        const imageCount = (htmlContent.match(/<img/g) || []).length;
        
        const baseHeight = lines * lineHeight;
        const tableHeight = tableCount * 50; // 每个表格约50mm
        const imageHeight = imageCount * 30; // 每个图片约30mm
        
        return baseHeight + tableHeight + imageHeight;
    }

    /**
     * 获取打印设置 - 返回当前的打印配置
     * @returns {Object} 打印设置对象
     */
    getPrintSettings() {
        return {
            pageSize: this.renderConfig.pageSize,
            orientation: this.renderConfig.orientation,
            margins: this.renderConfig.margins,
            removeBackground: this.renderConfig.removeBackground,
            enablePageBreaks: this.renderConfig.enablePageBreaks,
            printOptimized: this.renderConfig.printOptimized
        };
    }

    /**
     * 设置打印配置 - 更新打印渲染器的配置
     * @param {Object} settings - 打印设置
     */
    setPrintSettings(settings) {
        this.renderConfig = {
            ...this.renderConfig,
            ...settings
        };
    }

    /**
     * 验证打印设置 - 检查打印设置的有效性
     * @param {Object} settings - 打印设置
     * @returns {Object} 验证结果
     */
    validatePrintSettings(settings) {
        const errors = [];
        const warnings = [];
        
        // 检查页面大小
        if (settings.pageSize && !this.supportedPageSizes.includes(settings.pageSize)) {
            errors.push(`不支持的页面大小: ${settings.pageSize}`);
        }
        
        // 检查页面方向
        if (settings.orientation && !this.supportedOrientations.includes(settings.orientation)) {
            errors.push(`不支持的页面方向: ${settings.orientation}`);
        }
        
        // 检查边距格式
        if (settings.margins && !/^\d+(mm|cm|in|pt)$/.test(settings.margins)) {
            warnings.push('边距格式可能不正确，建议使用如"20mm"的格式');
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
}
// #endregion

// #region 工厂函数
/**
 * 创建打印渲染器实例 - 工厂函数，方便创建打印渲染器
 * @param {Object} config - 渲染器配置
 * @returns {PrintRenderer} 打印渲染器实例
 */
export function createPrintRenderer(config = {}) {
    return new PrintRenderer(config);
}

/**
 * 创建预设打印渲染器 - 创建具有预定义配置的打印渲染器
 * @param {string} preset - 预设名称 ('default', 'minimal', 'high-quality', 'draft')
 * @param {Object} config - 额外配置
 * @returns {PrintRenderer} 打印渲染器实例
 */
export function createPresetPrintRenderer(preset = 'default', config = {}) {
    const presetConfigs = {
        default: {
            renderConfig: {
                pageSize: 'A4',
                orientation: 'portrait',
                margins: '20mm',
                removeBackground: false,
                enablePageBreaks: true,
                printPreview: true
            }
        },
        
        minimal: {
            renderConfig: {
                pageSize: 'A4',
                orientation: 'portrait',
                margins: '15mm',
                removeBackground: true,
                enablePageBreaks: true,
                printPreview: false,
                showPrintDialog: false
            }
        },