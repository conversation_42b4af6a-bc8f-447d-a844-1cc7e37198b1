/**
 * SmartOffice 2.0 统一渲染器基类
 * 所有渲染器的统一基础类，提供标准化的渲染接口和通用功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024-12-19
 */

import { DocumentModel } from '../core/rendering/document-model.js';
import { StyleManager } from '../core/rendering/style-manager.js';
import { PositionManager } from '../core/rendering/position-manager.js';
import { renderServiceFactory } from '../core/rendering/shared-render-services.js';

/**
 * 统一渲染器基类
 * 定义所有渲染器的标准接口和通用功能
 */
export class UnifiedRenderer {
    /**
     * 构造函数
     * @param {Object} config - 渲染器配置
     */
    constructor(config = {}) {
        this.config = {
            // 渲染器基础信息
            name: 'UnifiedRenderer',
            version: '1.0.0',
            type: 'base',
            
            // 渲染配置
            format: 'html', // 'html', 'pdf', 'image', 'print'
            quality: 'high', // 'low', 'medium', 'high'
            theme: 'default',
            
            // 性能配置
            enableCache: true,
            enableOptimization: true,
            enableParallel: false,
            
            // 调试配置
            enableDebug: false,
            enableProfiling: false,
            
            // 错误处理
            errorHandling: 'throw', // 'throw', 'log', 'ignore'
            
            // 渲染选项
            options: {
                preserveWhitespace: false,
                minifyOutput: true,
                includeMetadata: true,
                validateOutput: true
            },
            
            ...config
        };
        
        // 管理器实例
        this.styleManager = null;
        this.positionManager = null;
        
        // 共享渲染服务
        this.sharedStyleService = null;
        this.sharedLayoutService = null;
        this.sharedPositionService = null;
        
        // 渲染状态
        this.isRendering = false;
        this.renderingDocument = null;
        this.renderingOptions = null;
        
        // 性能统计
        this.performanceStats = {
            totalRenders: 0,
            successfulRenders: 0,
            failedRenders: 0,
            averageRenderTime: 0,
            lastRenderTime: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
        
        // 渲染缓存
        this.renderCache = new Map();
        
        // 错误收集
        this.errors = [];
        this.warnings = [];
        
        // 事件监听器
        this.eventListeners = new Map();
        
        // 渲染钩子
        this.hooks = {
            beforeRender: [],
            afterRender: [],
            onError: [],
            onWarning: []
        };
        
        // 初始化共享服务
        this._initializeSharedServices();
        
        console.log(`[${this.config.name}] 统一渲染器初始化完成`);
    }
    
    /**
     * 初始化共享渲染服务
     * @private
     */
    _initializeSharedServices() {
        this.sharedLayoutService = renderServiceFactory.getLayoutService();
        console.log(`[${this.config.name}] 共享渲染服务初始化完成`);
    }
    
    /**
     * 设置样式管理器
     * @param {StyleManager} styleManager - 样式管理器实例
     */
    setStyleManager(styleManager) {
        if (!(styleManager instanceof StyleManager)) {
            throw new Error('样式管理器必须是StyleManager实例');
        }
        
        this.styleManager = styleManager;
        this.sharedStyleService = renderServiceFactory.getStyleService(styleManager);
        console.log(`[${this.config.name}] 样式管理器已设置`);
    }
    
    /**
     * 设置位置管理器
     * @param {PositionManager} positionManager - 位置管理器实例
     */
    setPositionManager(positionManager) {
        if (!(positionManager instanceof PositionManager)) {
            throw new Error('位置管理器必须是PositionManager实例');
        }
        
        this.positionManager = positionManager;
        this.sharedPositionService = renderServiceFactory.getPositionService(positionManager);
        console.log(`[${this.config.name}] 位置管理器已设置`);
    }
    
    /**
     * 通用样式处理方法
     * 子类可以直接调用或覆盖此方法
     * @param {DocumentModel} document - 文档模型
     * @param {Object} options - 处理选项
     * @returns {Promise<Object>} 处理后的样式
     * @protected
     */
    async _processStyles(document, options = {}) {
        if (!this.sharedStyleService) {
            console.warn(`[${this.config.name}] 共享样式服务未初始化，使用默认处理`);
            return this._getDefaultStyles();
        }
        
        return await this.sharedStyleService.processStyles(
            document, 
            options, 
            this.config.format
        );
    }
    
    /**
     * 通用布局处理方法
     * 子类可以直接调用或覆盖此方法
     * @param {DocumentModel} document - 文档模型
     * @param {Object} options - 处理选项
     * @returns {Promise<Object>} 处理后的布局
     * @protected
     */
    async _processLayout(document, options = {}) {
        if (!this.sharedLayoutService) {
            console.warn(`[${this.config.name}] 共享布局服务未初始化，使用默认处理`);
            return await document.calculateLayout();
        }
        
        return await this.sharedLayoutService.processLayout(
            document, 
            options, 
            this.config.format
        );
    }
    
    /**
     * 通用位置处理方法
     * 子类可以直接调用或覆盖此方法
     * @param {DocumentModel} document - 文档模型
     * @param {Object} layout - 布局信息
     * @param {Object} options - 处理选项
     * @returns {Promise<Object>} 处理后的位置
     * @protected
     */
    async _processPositions(document, layout, options = {}) {
        if (!this.sharedPositionService) {
            console.warn(`[${this.config.name}] 共享位置服务未初始化，使用默认处理`);
            return { elements: [] };
        }
        
        return await this.sharedPositionService.processPositions(
            document, 
            layout, 
            options, 
            this.config.format
        );
    }
    
    /**
     * 获取默认样式
     * @returns {Object} 默认样式
     * @protected
     */
    _getDefaultStyles() {
        return {
            css: 'body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }',
            format: this.config.format
        };
    }
    
    /**
     * 渲染文档
     * @param {DocumentModel|Object} document - 文档模型或文档数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 渲染结果
     */
    async render(document, options = {}) {
        if (this.isRendering) {
            throw new Error('渲染器正在执行中，请等待当前渲染完成');
        }
        
        const startTime = Date.now();
        this.isRendering = true;
        this.performanceStats.totalRenders++;
        
        try {
            console.log(`[${this.config.name}] 开始渲染`);
            
            // 准备渲染
            const preparedData = await this._prepareRender(document, options);
            
            // 执行渲染
            const result = await this._executeRender(preparedData);
            
            // 后处理
            const finalResult = await this._postProcessRender(result, preparedData);
            
            // 更新统计
            const renderTime = Date.now() - startTime;
            this._updateStats(renderTime, true);
            
            console.log(`[${this.config.name}] 渲染完成，耗时: ${renderTime}ms`);
            
            return {
                success: true,
                result: finalResult,
                renderTime,
                metadata: {
                    renderer: this.config.name,
                    version: this.config.version,
                    format: this.config.format,
                    timestamp: new Date().toISOString()
                }
            };
            
        } catch (error) {
            const renderTime = Date.now() - startTime;
            this._updateStats(renderTime, false);
            this._handleError(error);
            
            console.error(`[${this.config.name}] 渲染失败:`, error);
            
            return {
                success: false,
                error: error.message,
                renderTime,
                metadata: {
                    renderer: this.config.name,
                    version: this.config.version,
                    format: this.config.format,
                    timestamp: new Date().toISOString()
                }
            };
            
        } finally {
            this.isRendering = false;
            this.renderingDocument = null;
            this.renderingOptions = null;
        }
    }
    
    /**
     * 批量渲染
     * @param {Array} documents - 文档列表
     * @param {Object} options - 渲染选项
     * @returns {Promise<Array>} 渲染结果列表
     */
    async batchRender(documents, options = {}) {
        const results = [];
        
        if (this.config.enableParallel) {
            // 并行渲染
            const promises = documents.map(doc => this.render(doc, options));
            const batchResults = await Promise.allSettled(promises);
            
            for (const result of batchResults) {
                if (result.status === 'fulfilled') {
                    results.push(result.value);
                } else {
                    results.push({
                        success: false,
                        error: result.reason.message
                    });
                }
            }
        } else {
            // 串行渲染
            for (const document of documents) {
                const result = await this.render(document, options);
                results.push(result);
            }
        }
        
        return results;
    }
    
    /**
     * 预览渲染
     * @param {DocumentModel|Object} document - 文档模型或文档数据
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 预览结果
     */
    async preview(document, options = {}) {
        const previewOptions = {
            ...options,
            quality: 'medium', // 预览使用中等质量
            enableOptimization: false, // 预览不优化
            includeMetadata: false // 预览不包含元数据
        };
        
        return await this.render(document, previewOptions);
    }
    
    /**
     * 添加渲染钩子
     * @param {string} hookName - 钩子名称
     * @param {Function} callback - 回调函数
     */
    addHook(hookName, callback) {
        if (!this.hooks[hookName]) {
            throw new Error(`不支持的钩子: ${hookName}`);
        }
        
        if (typeof callback !== 'function') {
            throw new Error('钩子回调必须是函数');
        }
        
        this.hooks[hookName].push(callback);
        console.log(`[${this.config.name}] 钩子已添加: ${hookName}`);
    }
    
    /**
     * 移除渲染钩子
     * @param {string} hookName - 钩子名称
     * @param {Function} callback - 回调函数
     */
    removeHook(hookName, callback) {
        if (!this.hooks[hookName]) {
            return;
        }
        
        const index = this.hooks[hookName].indexOf(callback);
        if (index > -1) {
            this.hooks[hookName].splice(index, 1);
            console.log(`[${this.config.name}] 钩子已移除: ${hookName}`);
        }
    }
    
    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} listener - 监听器函数
     */
    on(event, listener) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        
        this.eventListeners.get(event).push(listener);
        console.log(`[${this.config.name}] 事件监听器已添加: ${event}`);
    }
    
    /**
     * 移除事件监听器
     * @param {string} event - 事件名称
     * @param {Function} listener - 监听器函数
     */
    off(event, listener) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(listener);
            if (index > -1) {
                listeners.splice(index, 1);
                console.log(`[${this.config.name}] 事件监听器已移除: ${event}`);
            }
        }
    }
    
    /**
     * 获取性能统计
     * @returns {Object} 性能统计信息
     */
    getPerformanceStats() {
        return {
            ...this.performanceStats,
            cacheSize: this.renderCache.size,
            isRendering: this.isRendering,
            renderer: this.config.name
        };
    }
    
    /**
     * 获取渲染器信息
     * @returns {Object} 渲染器信息
     */
    getInfo() {
        return {
            name: this.config.name,
            version: this.config.version,
            type: this.config.type,
            format: this.config.format,
            supportedFormats: this.getSupportedFormats(),
            features: this.getFeatures(),
            status: this.isRendering ? 'rendering' : 'idle'
        };
    }
    
    /**
     * 获取支持的格式
     * @returns {Array} 支持的格式列表
     */
    getSupportedFormats() {
        // 子类应该重写此方法
        return [this.config.format];
    }
    
    /**
     * 获取渲染器特性
     * @returns {Array} 特性列表
     */
    getFeatures() {
        // 子类应该重写此方法
        return ['basic-rendering'];
    }
    
    /**
     * 验证文档
     * @param {DocumentModel|Object} document - 文档
     * @returns {Object} 验证结果
     */
    validateDocument(document) {
        const errors = [];
        const warnings = [];
        
        // 基础验证
        if (!document) {
            errors.push('文档不能为空');
        }
        
        // 如果是DocumentModel实例，使用其验证方法
        if (document instanceof DocumentModel) {
            const validation = document.validate();
            errors.push(...validation.errors);
            warnings.push(...validation.warnings);
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    
    /**
     * 清除缓存
     */
    clearCache() {
        this.renderCache.clear();
        console.log(`[${this.config.name}] 渲染缓存已清除`);
    }
    
    /**
     * 重置统计信息
     */
    resetStats() {
        this.performanceStats = {
            totalRenders: 0,
            successfulRenders: 0,
            failedRenders: 0,
            averageRenderTime: 0,
            lastRenderTime: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
        
        this.errors = [];
        this.warnings = [];
        
        console.log(`[${this.config.name}] 统计信息已重置`);
    }
    
    /**
     * 更新配置
     * @param {Object} newConfig - 新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log(`[${this.config.name}] 配置已更新`);
    }
    
    /**
     * 销毁渲染器
     */
    destroy() {
        this.renderCache.clear();
        this.eventListeners.clear();
        this.errors = [];
        this.warnings = [];
        
        // 清除钩子
        for (const hookName in this.hooks) {
            this.hooks[hookName] = [];
        }
        
        console.log(`[${this.config.name}] 渲染器已销毁`);
    }
    
    // ==================== 受保护的方法（子类可重写） ====================
    
    /**
     * 准备渲染
     * @param {DocumentModel|Object} document - 文档
     * @param {Object} options - 选项
     * @returns {Promise<Object>} 准备好的数据
     * @protected
     */
    async _prepareRender(document, options) {
        // 执行前置钩子
        await this._executeHooks('beforeRender', { document, options });
        
        // 确保文档是DocumentModel实例
        const documentModel = document instanceof DocumentModel 
            ? document 
            : new DocumentModel(document);
        
        // 验证文档
        const validation = this.validateDocument(documentModel);
        if (!validation.isValid) {
            throw new Error(`文档验证失败: ${validation.errors.join(', ')}`);
        }
        
        // 合并选项
        const mergedOptions = {
            ...this.config.options,
            ...options,
            format: options.format || this.config.format,
            quality: options.quality || this.config.quality,
            theme: options.theme || this.config.theme
        };
        
        // 检查缓存
        const cacheKey = this._generateCacheKey(documentModel, mergedOptions);
        if (this.config.enableCache && this.renderCache.has(cacheKey)) {
            this.performanceStats.cacheHits++;
            console.log(`[${this.config.name}] 缓存命中: ${cacheKey}`);
            return {
                fromCache: true,
                result: this.renderCache.get(cacheKey)
            };
        }
        
        this.performanceStats.cacheMisses++;
        
        // 保存渲染状态
        this.renderingDocument = documentModel;
        this.renderingOptions = mergedOptions;
        
        return {
            document: documentModel,
            options: mergedOptions,
            cacheKey,
            fromCache: false
        };
    }
    
    /**
     * 执行渲染
     * @param {Object} preparedData - 准备好的数据
     * @returns {Promise<Object>} 渲染结果
     * @protected
     */
    async _executeRender(preparedData) {
        // 如果是缓存结果，直接返回
        if (preparedData.fromCache) {
            return preparedData.result;
        }
        
        const { document, options } = preparedData;
        
        // 子类应该重写此方法实现具体的渲染逻辑
        throw new Error('子类必须实现 _executeRender 方法');
    }
    
    /**
     * 后处理渲染结果
     * @param {Object} result - 渲染结果
     * @param {Object} preparedData - 准备好的数据
     * @returns {Promise<Object>} 最终结果
     * @protected
     */
    async _postProcessRender(result, preparedData) {
        // 缓存结果
        if (this.config.enableCache && !preparedData.fromCache) {
            this.renderCache.set(preparedData.cacheKey, result);
        }
        
        // 验证输出
        if (preparedData.options.validateOutput) {
            this._validateOutput(result);
        }
        
        // 执行后置钩子
        await this._executeHooks('afterRender', { result, preparedData });
        
        return result;
    }
    
    /**
     * 验证输出
     * @param {Object} result - 渲染结果
     * @protected
     */
    _validateOutput(result) {
        if (!result) {
            throw new Error('渲染结果不能为空');
        }
        
        // 子类可以重写此方法添加特定的验证逻辑
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 生成缓存键
     * @param {DocumentModel} document - 文档模型
     * @param {Object} options - 选项
     * @returns {string} 缓存键
     * @private
     */
    _generateCacheKey(document, options) {
        const documentHash = this._hashObject(document.getData());
        const optionsHash = this._hashObject(options);
        return `${this.config.name}_${documentHash}_${optionsHash}`;
    }
    
    /**
     * 对象哈希
     * @param {Object} obj - 对象
     * @returns {string} 哈希值
     * @private
     */
    _hashObject(obj) {
        const str = JSON.stringify(obj, Object.keys(obj).sort());
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash.toString(36);
    }
    
    /**
     * 执行钩子
     * @param {string} hookName - 钩子名称
     * @param {Object} data - 数据
     * @private
     */
    async _executeHooks(hookName, data) {
        const hooks = this.hooks[hookName] || [];
        
        for (const hook of hooks) {
            try {
                await hook(data);
            } catch (error) {
                console.warn(`[${this.config.name}] 钩子执行失败 (${hookName}):`, error);
                await this._executeHooks('onWarning', { hookName, error, data });
            }
        }
    }
    
    /**
     * 更新统计信息
     * @param {number} renderTime - 渲染时间
     * @param {boolean} success - 是否成功
     * @private
     */
    _updateStats(renderTime, success) {
        this.performanceStats.lastRenderTime = renderTime;
        
        if (success) {
            this.performanceStats.successfulRenders++;
            
            // 更新平均渲染时间
            const total = this.performanceStats.successfulRenders;
            this.performanceStats.averageRenderTime = 
                (this.performanceStats.averageRenderTime * (total - 1) + renderTime) / total;
        } else {
            this.performanceStats.failedRenders++;
        }
    }
    
    /**
     * 处理错误
     * @param {Error} error - 错误对象
     * @private
     */
    _handleError(error) {
        this.errors.push({
            message: error.message,
            stack: error.stack,
            timestamp: Date.now(),
            renderer: this.config.name
        });
        
        // 执行错误钩子
        this._executeHooks('onError', { error });
        
        // 触发错误事件
        this._emitEvent('error', { error });
        
        if (this.config.errorHandling === 'throw') {
            throw error;
        } else if (this.config.errorHandling === 'log') {
            console.error(`[${this.config.name}] 错误:`, error);
        }
        // 'ignore' 模式不做任何处理
    }
    
    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {Object} data - 事件数据
     * @private
     */
    _emitEvent(event, data) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            for (const listener of listeners) {
                try {
                    listener(data);
                } catch (error) {
                    console.warn(`[${this.config.name}] 事件监听器执行失败 (${event}):`, error);
                }
            }
        }
    }
}